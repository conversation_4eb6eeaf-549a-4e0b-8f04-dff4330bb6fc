#!/usr/bin/env bash
# Modified

source ./common.sh
source ./my-env.sh

function projectsCommit() {
  CMD="git commit -m \"$@\" -a"

  for project in "${PROJECTS[@]}"
  do
    echo "Run $CMD in  $project"
    echo "----------------------------------------------------------------"
    cd $ROOT_DIR/$project
    if git status --porcelain | grep .; then
      git commit -m "$@" -a
    else
      echo "REPO $project IS CLEAN"
    fi
    echo "----------------------------------------------------------------"
  done
}


function projects() {
  runInProjects "$@"
}

function check_set_branch() {
  BRANCH=$1
  if [ "$BRANCH" = "" ] ; then
    echo "BRANCH variable is not set";
    exit 0
  fi
}

function check_clean_branch() {
  if git status --porcelain | grep .; then
    echo ""
    echo "REPO $project IS NOT CLEAN. PLEASE COMMIT YOUR CHANGE!!!"
    echo ""
    exit 0
  else
    echo "REPO $project IS CLEAN"
  fi
}

function check_using_branch() {
  BRANCH=$1
  CURR_BRANCH=`git branch --show-current`
  if [ "$BRANCH" != "$CURR_BRANCH" ] ; then
    echo -e "\n";
    echo -e "BRANCH = $BRANCH, CURR_BRANCH = $CURR_BRANCH, BRANCH != CURR_BRANCH ";
    echo -e "You need to switch to '$BRANCH' branch env ";
    echo -e "\n";
    exit 0
  fi
}

function git_branch() {
  COMMAND=$1
  shift
  BRANCH=$1
  shift

  if [ "$COMMAND" = "env" ] ; then
    check_set_branch $BRANCH
    projects check_clean_branch
    projects git checkout $BRANCH
    projects check_using_branch $BRANCH
  elif [ "$COMMAND" = "env:new" ] ; then
    check_set_branch $BRANCH
    projects git checkout -b $BRANCH
    projects check_using_branch $BRANCH
  elif [ "$COMMAND" = "reset:origin" ] ; then
    projects check_using_branch $BRANCH
    projects git reset --hard origin/$BRANCH
  elif [ "$COMMAND" = "pull" ] ; then
    projects check_using_branch $BRANCH
    projects git pull origin $BRANCH
  elif [ "$COMMAND" = "pull:develop" ] ; then
    projects check_using_branch $BRANCH
    projects git pull origin develop 
  elif [ "$COMMAND" = "commit" ] ; then
    projects check_using_branch $BRANCH
    projects git add .
    projectsCommit "$@"
  elif [ "$COMMAND" = "push" ] ; then
    projects check_using_branch $BRANCH
    projects git push origin $BRANCH
  fi
}

COMMAND=$1
shift

if [ "$COMMAND" = "develop:env" ] ; then
  git_branch env develop
elif [ "$COMMAND" = "develop:reset:origin" ] ; then
  git_branch reset:origin develop
elif [ "$COMMAND" = "develop:pull" ] ; then
  git_branch pull develop
elif [ "$COMMAND" = "develop:merge" ] ; then
  check_set_branch develop
  runInProjects git merge $1
elif [ "$COMMAND" = "develop:commit" ] ; then
  git_branch commit develop $@
elif [ "$COMMAND" = "develop:push" ] ; then
  git_branch push develop

elif [ "$COMMAND" = "working:env" ] ; then
  git_branch env $WORKING_BRANCH
elif [ "$COMMAND" = "working:env:new" ] ; then
  git_branch env:new $WORKING_BRANCH
elif [ "$COMMAND" = "working:reset:origin" ] ; then
  git_branch reset:origin $WORKING_BRANCH
elif [ "$COMMAND" = "working:pull" ] ; then
  git_branch pull $WORKING_BRANCH
elif [ "$COMMAND" = "working:pull:develop" ] ; then
  git_branch pull:develop $WORKING_BRANCH
elif [ "$COMMAND" = "working:merge" ] ; then
  check_set_branch $WORKING_BRANCH
  runInProjects git merge $1
elif [ "$COMMAND" = "working:commit" ] ; then
  git_branch commit $WORKING_BRANCH $@
elif [ "$COMMAND" = "working:push" ] ; then
  git_branch push $WORKING_BRANCH

elif [ "$COMMAND" = "fetch:origin" ] ; then
  runInProjects git fetch origin $1
elif [ "$COMMAND" = "fetch:prune" ] ; then
  runInProjects git fetch --prune
elif [ "$COMMAND" = "status" ] ; then
  runInProjects git status
elif [ "$COMMAND" = "pull" ] ; then
  runInProjects git pull $@
elif [ "$COMMAND" = "branch:delete" ] ; then
  runInProjects git branch -D $@
  runInProjects git push origin -d $@
elif [ "$COMMAND" = "branch:list" ] ; then
  runInProjects git branch -r
elif [ "$COMMAND" = "push" ] ; then
  runInProjects git push $@

elif [ "$COMMAND" = "help" ] ; then
  echo "Usage: "
  echo " status     Run git status in each project"
else
  runInProjects git $@
fi
