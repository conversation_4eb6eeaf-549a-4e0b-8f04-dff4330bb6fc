2025-07-11T10:46:55.726+07:00  INFO 62125 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 62125 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-11T10:46:55.726+07:00  INFO 62125 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data"
2025-07-11T10:46:56.532+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.637+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 101 ms. Found 22 JPA repository interfaces.
2025-07-11T10:46:56.648+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.650+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-11T10:46:56.650+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.657+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-07-11T10:46:56.658+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.662+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-07-11T10:46:56.662+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.666+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T10:46:56.681+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.687+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 1 JPA repository interface.
2025-07-11T10:46:56.700+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.705+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-07-11T10:46:56.709+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.712+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-07-11T10:46:56.712+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.713+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T10:46:56.718+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.724+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-11T10:46:56.729+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.732+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-11T10:46:56.732+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.736+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T10:46:56.737+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.744+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-07-11T10:46:56.745+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.747+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-11T10:46:56.748+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.748+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T10:46:56.748+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.749+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-07-11T10:46:56.749+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.753+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-11T10:46:56.753+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.755+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-11T10:46:56.755+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.755+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T10:46:56.755+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.766+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-07-11T10:46:56.775+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.780+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-07-11T10:46:56.780+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.783+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-11T10:46:56.783+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.788+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-11T10:46:56.788+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.794+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-07-11T10:46:56.794+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.799+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T10:46:56.799+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.802+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-07-11T10:46:56.802+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.812+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-07-11T10:46:56.812+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.827+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 25 JPA repository interfaces.
2025-07-11T10:46:56.841+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.854+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 19 JPA repository interfaces.
2025-07-11T10:46:56.854+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.858+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-11T10:46:56.859+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.860+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-11T10:46:56.865+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.865+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T10:46:56.866+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.873+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-07-11T10:46:56.876+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.914+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 38 ms. Found 67 JPA repository interfaces.
2025-07-11T10:46:56.915+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.916+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-11T10:46:56.916+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:46:56.919+07:00  INFO 62125 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-11T10:46:57.137+07:00  INFO 62125 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-11T10:46:57.141+07:00  INFO 62125 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-11T10:46:57.467+07:00  WARN 62125 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-11T10:46:57.683+07:00  INFO 62125 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-11T10:46:57.686+07:00  INFO 62125 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-11T10:46:57.698+07:00  INFO 62125 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-11T10:46:57.698+07:00  INFO 62125 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1818 ms
2025-07-11T10:46:57.746+07:00  WARN 62125 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T10:46:57.746+07:00  INFO 62125 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-11T10:46:57.851+07:00  INFO 62125 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@1b1d2d7b
2025-07-11T10:46:57.851+07:00  INFO 62125 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-11T10:46:57.856+07:00  WARN 62125 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T10:46:57.856+07:00  INFO 62125 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-11T10:46:57.873+07:00  INFO 62125 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@298154d4
2025-07-11T10:46:57.873+07:00  INFO 62125 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-11T10:46:57.874+07:00  WARN 62125 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T10:46:57.874+07:00  INFO 62125 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-11T10:46:58.698+07:00  INFO 62125 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2cf073da
2025-07-11T10:46:58.699+07:00  INFO 62125 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-11T10:46:58.699+07:00  WARN 62125 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T10:46:58.699+07:00  INFO 62125 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-11T10:46:58.705+07:00  INFO 62125 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@3d26c82d
2025-07-11T10:46:58.705+07:00  INFO 62125 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-11T10:46:58.705+07:00  INFO 62125 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-11T10:46:58.763+07:00  INFO 62125 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-11T10:46:58.766+07:00  INFO 62125 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@50626d22{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.11329955891331857128/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@734db023{STARTED}}
2025-07-11T10:46:58.766+07:00  INFO 62125 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@50626d22{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.11329955891331857128/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@734db023{STARTED}}
2025-07-11T10:46:58.768+07:00  INFO 62125 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@2f26634a{STARTING}[12.0.15,sto=0] @3658ms
2025-07-11T10:46:58.837+07:00  INFO 62125 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11T10:46:58.877+07:00  INFO 62125 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-11T10:46:58.898+07:00  INFO 62125 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-11T10:46:59.061+07:00  INFO 62125 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-11T10:46:59.147+07:00  WARN 62125 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-11T10:46:59.846+07:00  INFO 62125 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-11T10:46:59.855+07:00  INFO 62125 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@5facf061] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-11T10:47:00.189+07:00  INFO 62125 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T10:47:00.431+07:00  INFO 62125 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "cloud.datatp.fforwarder.price", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.tms", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-07-11T10:47:00.434+07:00  INFO 62125 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-11T10:47:00.441+07:00  INFO 62125 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11T10:47:00.443+07:00  INFO 62125 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-11T10:47:00.477+07:00  INFO 62125 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-11T10:47:00.488+07:00  WARN 62125 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-11T10:47:03.753+07:00  INFO 62125 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-11T10:47:03.754+07:00  INFO 62125 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@75f12f98] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-11T10:47:04.032+07:00  WARN 62125 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-07-11T10:47:04.032+07:00  WARN 62125 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-07-11T10:47:04.040+07:00  WARN 62125 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-07-11T10:47:04.040+07:00  WARN 62125 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-07-11T10:47:04.054+07:00  WARN 62125 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-07-11T10:47:04.054+07:00  WARN 62125 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-07-11T10:47:04.751+07:00  INFO 62125 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T10:47:04.814+07:00  INFO 62125 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-11T10:47:04.821+07:00  INFO 62125 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-11T10:47:04.821+07:00  INFO 62125 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-11T10:47:04.830+07:00  WARN 62125 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-11T10:47:04.985+07:00  INFO 62125 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-11T10:47:05.450+07:00  INFO 62125 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-11T10:47:05.453+07:00  INFO 62125 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-11T10:47:05.492+07:00  INFO 62125 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-11T10:47:05.538+07:00  INFO 62125 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-11T10:47:05.648+07:00  INFO 62125 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-11T10:47:05.678+07:00  INFO 62125 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-11T10:47:05.699+07:00  INFO 62125 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 2332973ms : this is harmless.
2025-07-11T10:47:05.707+07:00  INFO 62125 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-11T10:47:05.710+07:00  INFO 62125 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-11T10:47:05.721+07:00  INFO 62125 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 8429180ms : this is harmless.
2025-07-11T10:47:05.723+07:00  INFO 62125 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-11T10:47:05.735+07:00  INFO 62125 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-11T10:47:05.736+07:00  INFO 62125 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-11T10:47:08.308+07:00  INFO 62125 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 11/07/2025@10:45:00+0700 to 11/07/2025@11:00:00+0700
2025-07-11T10:47:08.308+07:00  INFO 62125 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 11/07/2025@10:45:00+0700 to 11/07/2025@11:00:00+0700
2025-07-11T10:47:09.085+07:00  INFO 62125 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-11T10:47:09.085+07:00  INFO 62125 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-11T10:47:09.086+07:00  WARN 62125 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-11T10:47:09.355+07:00  INFO 62125 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-11T10:47:09.356+07:00  INFO 62125 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-11T10:47:09.356+07:00  INFO 62125 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-11T10:47:09.356+07:00  INFO 62125 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-11T10:47:09.356+07:00  INFO 62125 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-11T10:47:11.098+07:00  WARN 62125 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: e5e4efb2-3c60-408e-a8dc-769500969d8d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-11T10:47:11.101+07:00  INFO 62125 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-11T10:47:11.430+07:00  INFO 62125 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-11T10:47:11.430+07:00  INFO 62125 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-11T10:47:11.431+07:00  INFO 62125 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-11T10:47:11.431+07:00  INFO 62125 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-11T10:47:11.431+07:00  INFO 62125 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-11T10:47:11.431+07:00  INFO 62125 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-11T10:47:11.431+07:00  INFO 62125 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-11T10:47:11.431+07:00  INFO 62125 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-11T10:47:11.431+07:00  INFO 62125 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-11T10:47:11.431+07:00  INFO 62125 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-11T10:47:11.431+07:00  INFO 62125 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-11T10:47:11.431+07:00  INFO 62125 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-11T10:47:11.435+07:00  INFO 62125 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-11T10:47:11.435+07:00  INFO 62125 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-11T10:47:11.435+07:00  INFO 62125 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-11T10:47:11.518+07:00  INFO 62125 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11T10:47:11.519+07:00  INFO 62125 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-11T10:47:11.520+07:00  INFO 62125 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-11T10:47:11.541+07:00  INFO 62125 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@1f055b23{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-11T10:47:11.542+07:00  INFO 62125 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-11T10:47:11.543+07:00  INFO 62125 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-11T10:47:11.580+07:00  INFO 62125 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-11T10:47:11.580+07:00  INFO 62125 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-11T10:47:11.586+07:00  INFO 62125 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 16.145 seconds (process running for 16.476)
2025-07-11T10:48:06.558+07:00  INFO 62125 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:48:07.053+07:00  INFO 62125 --- [botTaskExecutor-1] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6169] [company=beehph] type=ZALO for 11/07/2025@10:48:07+0700
2025-07-11T10:48:07.053+07:00  INFO 62125 --- [botTaskExecutor-1] c.d.f.s.message.MessageQueueManager      : Added message [6169] - scheduled at 11/07/2025@10:48:07+0700 - current session (11/07/2025@10:45:00+0700 to 11/07/2025@11:00:00+0700)

2025-07-11T10:48:14.586+07:00  INFO 62125 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:48:14.590+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:48:14.591+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:48:14.592+07:00  INFO 62125 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:48:47.645+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:48:47.646+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:49:03.675+07:00  INFO 62125 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:49:03.731+07:00  INFO 62125 --- [botTaskExecutor-2] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6170] [company=beehph] type=ZALO for 11/07/2025@10:49:03+0700
2025-07-11T10:49:03.732+07:00  INFO 62125 --- [botTaskExecutor-2] c.d.f.s.message.MessageQueueManager      : Added message [6170] - scheduled at 11/07/2025@10:49:03+0700 - current session (11/07/2025@10:45:00+0700 to 11/07/2025@11:00:00+0700)

2025-07-11T10:49:17.700+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:49:17.702+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:49:45.757+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:49:45.765+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:50:06.804+07:00  INFO 62125 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:50:06.809+07:00  INFO 62125 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T10:50:06.856+07:00  INFO 62125 --- [botTaskExecutor-3] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6171] [company=beehph] type=ZALO for 11/07/2025@10:50:06+0700
2025-07-11T10:50:06.856+07:00  INFO 62125 --- [botTaskExecutor-3] c.d.f.s.message.MessageQueueManager      : Added message [6171] - scheduled at 11/07/2025@10:50:06+0700 - current session (11/07/2025@10:45:00+0700 to 11/07/2025@11:00:00+0700)

2025-07-11T10:50:13.828+07:00  INFO 62125 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:50:13.831+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:50:13.831+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:50:13.832+07:00  INFO 62125 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:50:47.886+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:50:47.890+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:51:02.915+07:00  INFO 62125 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:51:02.974+07:00  INFO 62125 --- [botTaskExecutor-1] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6172] [company=beehph] type=ZALO for 11/07/2025@10:51:02+0700
2025-07-11T10:51:02.975+07:00  INFO 62125 --- [botTaskExecutor-1] c.d.f.s.message.MessageQueueManager      : Added message [6172] - scheduled at 11/07/2025@10:51:02+0700 - current session (11/07/2025@10:45:00+0700 to 11/07/2025@11:00:00+0700)

2025-07-11T10:51:16.941+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:51:16.944+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:51:44.989+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:51:44.990+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:52:06.033+07:00  INFO 62125 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:52:06.076+07:00  INFO 62125 --- [botTaskExecutor-2] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6173] [company=beehph] type=ZALO for 11/07/2025@10:52:06+0700
2025-07-11T10:52:06.076+07:00  INFO 62125 --- [botTaskExecutor-2] c.d.f.s.message.MessageQueueManager      : Added message [6173] - scheduled at 11/07/2025@10:52:06+0700 - current session (11/07/2025@10:45:00+0700 to 11/07/2025@11:00:00+0700)

2025-07-11T10:52:18.053+07:00  INFO 62125 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:52:18.057+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:52:18.057+07:00 DEBUG 62125 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:52:18.058+07:00  INFO 62125 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:52:36.031+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@1f055b23{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-11T10:52:36.032+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-11T10:52:36.032+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-11T10:52:36.032+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-11T10:52:36.033+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-11T10:52:36.033+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-11T10:52:36.033+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-11T10:52:36.033+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-11T10:52:36.033+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-11T10:52:36.033+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-11T10:52:36.033+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-11T10:52:36.033+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-11T10:52:36.033+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-11T10:52:36.033+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-11T10:52:36.033+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-11T10:52:36.033+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-11T10:52:36.048+07:00  INFO 62125 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:52:36.105+07:00  INFO 62125 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-11T10:52:36.108+07:00  INFO 62125 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-11T10:52:36.128+07:00  INFO 62125 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T10:52:36.130+07:00  INFO 62125 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T10:52:36.130+07:00  INFO 62125 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-11T10:52:36.132+07:00  INFO 62125 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-11T10:52:36.132+07:00  INFO 62125 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-11T10:52:36.278+07:00  INFO 62125 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-11T10:52:36.278+07:00  INFO 62125 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-11T10:52:36.279+07:00  INFO 62125 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-11T10:52:36.279+07:00  INFO 62125 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-11T10:52:36.279+07:00  INFO 62125 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-11T10:52:36.282+07:00  INFO 62125 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@2f26634a{STOPPING}[12.0.15,sto=0]
2025-07-11T10:52:36.286+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-11T10:52:36.287+07:00  INFO 62125 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@50626d22{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.11329955891331857128/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@734db023{STOPPED}}
