2025-07-11T10:24:02.186+07:00  INFO 57956 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 57956 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-11T10:24:02.187+07:00  INFO 57956 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-console", "database", "db-schema-validate", "data"
2025-07-11T10:24:02.943+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.045+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 98 ms. Found 22 JPA repository interfaces.
2025-07-11T10:24:03.053+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.054+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-11T10:24:03.054+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.061+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-07-11T10:24:03.061+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.064+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-11T10:24:03.065+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.068+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T10:24:03.079+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.084+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-11T10:24:03.094+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.098+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-07-11T10:24:03.103+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.105+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-11T10:24:03.105+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.106+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T10:24:03.111+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.118+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-07-11T10:24:03.123+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.126+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-11T10:24:03.126+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.130+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T10:24:03.132+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.140+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 12 JPA repository interfaces.
2025-07-11T10:24:03.140+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.144+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-07-11T10:24:03.144+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.144+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T10:24:03.144+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.145+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-11T10:24:03.145+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.150+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-11T10:24:03.150+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.152+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-11T10:24:03.152+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.152+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T10:24:03.152+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.162+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-07-11T10:24:03.173+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.180+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-07-11T10:24:03.180+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.183+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-11T10:24:03.183+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.187+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-11T10:24:03.187+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.192+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-07-11T10:24:03.192+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.196+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T10:24:03.196+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.199+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-11T10:24:03.199+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.208+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-07-11T10:24:03.209+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.223+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 25 JPA repository interfaces.
2025-07-11T10:24:03.236+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.249+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 19 JPA repository interfaces.
2025-07-11T10:24:03.249+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.253+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T10:24:03.253+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.255+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-11T10:24:03.259+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.260+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T10:24:03.260+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.267+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-11T10:24:03.272+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.311+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 38 ms. Found 67 JPA repository interfaces.
2025-07-11T10:24:03.311+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.312+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-11T10:24:03.313+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:24:03.315+07:00  INFO 57956 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-11T10:24:03.534+07:00  INFO 57956 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-11T10:24:03.538+07:00  INFO 57956 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-11T10:24:03.805+07:00  WARN 57956 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-11T10:24:03.999+07:00  INFO 57956 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-11T10:24:04.001+07:00  INFO 57956 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-11T10:24:04.012+07:00  INFO 57956 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-11T10:24:04.013+07:00  INFO 57956 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1699 ms
2025-07-11T10:24:04.060+07:00  WARN 57956 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T10:24:04.060+07:00  INFO 57956 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-11T10:24:04.157+07:00  INFO 57956 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@1c9fa12
2025-07-11T10:24:04.158+07:00  INFO 57956 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-11T10:24:04.162+07:00  WARN 57956 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T10:24:04.162+07:00  INFO 57956 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-11T10:24:04.167+07:00  INFO 57956 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@474aeac5
2025-07-11T10:24:04.167+07:00  INFO 57956 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-11T10:24:04.168+07:00  WARN 57956 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T10:24:04.168+07:00  INFO 57956 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-11T10:24:04.601+07:00  INFO 57956 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6e1ef044
2025-07-11T10:24:04.601+07:00  INFO 57956 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-11T10:24:04.601+07:00  WARN 57956 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T10:24:04.601+07:00  INFO 57956 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-11T10:24:04.611+07:00  INFO 57956 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@5af7a7
2025-07-11T10:24:04.611+07:00  INFO 57956 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-11T10:24:04.611+07:00  INFO 57956 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-11T10:24:04.665+07:00  INFO 57956 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-11T10:24:04.668+07:00  INFO 57956 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@7cc742d{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13082278128195206837/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@22bf2a0d{STARTED}}
2025-07-11T10:24:04.668+07:00  INFO 57956 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@7cc742d{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13082278128195206837/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@22bf2a0d{STARTED}}
2025-07-11T10:24:04.670+07:00  INFO 57956 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@2810d9{STARTING}[12.0.15,sto=0] @3036ms
2025-07-11T10:24:04.731+07:00  INFO 57956 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11T10:24:04.766+07:00  INFO 57956 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-11T10:24:04.784+07:00  INFO 57956 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-11T10:24:04.916+07:00  INFO 57956 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-11T10:24:04.949+07:00  WARN 57956 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-11T10:24:05.555+07:00  INFO 57956 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-11T10:24:05.563+07:00  INFO 57956 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@156eb310] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-11T10:24:05.659+07:00  INFO 57956 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T10:24:05.821+07:00  INFO 57956 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "cloud.datatp.fforwarder.price", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-07-11T10:24:05.824+07:00  INFO 57956 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "validate",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-11T10:24:05.831+07:00  INFO 57956 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11T10:24:05.833+07:00  INFO 57956 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-11T10:24:05.863+07:00  INFO 57956 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-11T10:24:05.867+07:00  WARN 57956 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-11T10:24:08.898+07:00  INFO 57956 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-11T10:24:08.899+07:00  INFO 57956 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6f46692c] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-11T10:24:08.998+07:00  INFO 57956 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T10:24:09.033+07:00  INFO 57956 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-11T10:24:09.037+07:00  INFO 57956 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-11T10:24:09.037+07:00  INFO 57956 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-11T10:24:09.044+07:00  WARN 57956 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-11T10:24:09.212+07:00  INFO 57956 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-11T10:24:09.711+07:00  INFO 57956 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-11T10:24:09.714+07:00  INFO 57956 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-11T10:24:09.747+07:00  INFO 57956 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-11T10:24:09.790+07:00  INFO 57956 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-11T10:24:09.910+07:00  INFO 57956 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-11T10:24:09.940+07:00  INFO 57956 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-11T10:24:09.967+07:00  INFO 57956 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 1179509ms : this is harmless.
2025-07-11T10:24:09.975+07:00  INFO 57956 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-11T10:24:09.979+07:00  INFO 57956 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-11T10:24:09.990+07:00  INFO 57956 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 7275715ms : this is harmless.
2025-07-11T10:24:09.992+07:00  INFO 57956 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-11T10:24:10.004+07:00  INFO 57956 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-11T10:24:10.005+07:00  INFO 57956 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-11T10:24:12.613+07:00  INFO 57956 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 9 messages for session 11/07/2025@10:15:00+0700 to 11/07/2025@10:30:00+0700
2025-07-11T10:24:12.613+07:00  INFO 57956 --- [main] c.d.f.s.message.MessageQueueManager      : 📤 message : ZALO - zalo-interactive-reminder-message - scheduled at 11/07/2025@10:15:05+0700
2025-07-11T10:24:12.613+07:00  INFO 57956 --- [main] c.d.f.s.message.MessageQueueManager      : 📤 message : ZALO - zalo-interactive-reminder-message - scheduled at 11/07/2025@10:16:06+0700
2025-07-11T10:24:12.613+07:00  INFO 57956 --- [main] c.d.f.s.message.MessageQueueManager      : 📤 message : ZALO - zalo-interactive-reminder-message - scheduled at 11/07/2025@10:17:04+0700
2025-07-11T10:24:12.613+07:00  INFO 57956 --- [main] c.d.f.s.message.MessageQueueManager      : 📤 message : ZALO - zalo-interactive-reminder-message - scheduled at 11/07/2025@10:18:07+0700
2025-07-11T10:24:12.614+07:00  INFO 57956 --- [main] c.d.f.s.message.MessageQueueManager      : 📤 message : ZALO - zalo-interactive-reminder-message - scheduled at 11/07/2025@10:19:04+0700
2025-07-11T10:24:12.614+07:00  INFO 57956 --- [main] c.d.f.s.message.MessageQueueManager      : 📤 message : ZALO - zalo-interactive-reminder-message - scheduled at 11/07/2025@10:20:06+0700
2025-07-11T10:24:12.614+07:00  INFO 57956 --- [main] c.d.f.s.message.MessageQueueManager      : 📤 message : ZALO - zalo-interactive-reminder-message - scheduled at 11/07/2025@10:21:03+0700
2025-07-11T10:24:12.614+07:00  INFO 57956 --- [main] c.d.f.s.message.MessageQueueManager      : 📤 message : ZALO - zalo-interactive-reminder-message - scheduled at 11/07/2025@10:22:06+0700
2025-07-11T10:24:12.614+07:00  INFO 57956 --- [main] c.d.f.s.message.MessageQueueManager      : 📤 message : ZALO - zalo-interactive-reminder-message - scheduled at 11/07/2025@10:23:02+0700
2025-07-11T10:24:12.614+07:00  INFO 57956 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 9 messages for session 11/07/2025@10:15:00+0700 to 11/07/2025@10:30:00+0700
2025-07-11T10:24:13.258+07:00  INFO 57956 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-11T10:24:13.258+07:00  INFO 57956 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-11T10:24:13.259+07:00  WARN 57956 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-11T10:24:13.522+07:00  INFO 57956 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-11T10:24:13.522+07:00  INFO 57956 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-11T10:24:13.522+07:00  INFO 57956 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-11T10:24:13.522+07:00  INFO 57956 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-11T10:24:13.522+07:00  INFO 57956 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-11T10:24:15.207+07:00  WARN 57956 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: e14a29e7-a1a0-4cb9-94b8-011d6e9a5069

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-11T10:24:15.211+07:00  INFO 57956 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-11T10:24:15.540+07:00  INFO 57956 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-11T10:24:15.540+07:00  INFO 57956 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-11T10:24:15.540+07:00  INFO 57956 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-11T10:24:15.540+07:00  INFO 57956 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-11T10:24:15.541+07:00  INFO 57956 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-11T10:24:15.541+07:00  INFO 57956 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-11T10:24:15.541+07:00  INFO 57956 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-11T10:24:15.541+07:00  INFO 57956 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-11T10:24:15.541+07:00  INFO 57956 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-11T10:24:15.541+07:00  INFO 57956 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-11T10:24:15.541+07:00  INFO 57956 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-11T10:24:15.541+07:00  INFO 57956 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-11T10:24:15.543+07:00  INFO 57956 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-11T10:24:15.543+07:00  INFO 57956 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-11T10:24:15.544+07:00  INFO 57956 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-11T10:24:15.622+07:00  INFO 57956 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11T10:24:15.622+07:00  INFO 57956 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-11T10:24:15.624+07:00  INFO 57956 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-07-11T10:24:15.632+07:00  INFO 57956 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@6d36c0cc{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-11T10:24:15.633+07:00  INFO 57956 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-11T10:24:15.634+07:00  INFO 57956 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-11T10:24:15.661+07:00  INFO 57956 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-11T10:24:15.661+07:00  INFO 57956 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-11T10:24:15.667+07:00  INFO 57956 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.794 seconds (process running for 14.033)
2025-07-11T10:24:22.854+07:00  INFO 57956 --- [qtp1002973859-35] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-11T10:24:45.360+07:00  INFO 57956 --- [qtp1002973859-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01bh9jebrzcxniiqphm4vl3fnr0
2025-07-11T10:24:45.360+07:00  INFO 57956 --- [qtp1002973859-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0d7mn7bb6vkyd1qkgy1s38j27b1
2025-07-11T10:24:45.445+07:00  INFO 57956 --- [qtp1002973859-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0d7mn7bb6vkyd1qkgy1s38j27b1, token = 7480a5882fe2ff58353c1adf6faca356
2025-07-11T10:24:45.446+07:00  INFO 57956 --- [qtp1002973859-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01bh9jebrzcxniiqphm4vl3fnr0, token = 7480a5882fe2ff58353c1adf6faca356
2025-07-11T10:24:45.955+07:00  INFO 57956 --- [qtp1002973859-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T10:24:45.955+07:00  INFO 57956 --- [qtp1002973859-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T10:24:47.397+07:00 DEBUG 57956 --- [qtp1002973859-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:24:47.397+07:00 DEBUG 57956 --- [qtp1002973859-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:25:04.669+07:00  INFO 57956 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:25:04.684+07:00  INFO 57956 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T10:25:04.828+07:00  INFO 57956 --- [botTaskExecutor-1] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6180] [company=beehph] type=ZALO for 11/07/2025@10:25:04+0700
2025-07-11T10:25:04.828+07:00  INFO 57956 --- [botTaskExecutor-1] c.d.f.s.message.MessageQueueManager      : Added message [6180] - scheduled at 11/07/2025@10:25:04+0700 - current session (11/07/2025@10:15:00+0700 to 11/07/2025@10:30:00+0700)

2025-07-11T10:25:18.743+07:00  INFO 57956 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-07-11T10:25:18.765+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:25:18.765+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01bh9jebrzcxniiqphm4vl3fnr0, remote user nhat.le
2025-07-11T10:25:18.766+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:25:18.766+07:00  INFO 57956 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:25:47.607+07:00 DEBUG 57956 --- [qtp1002973859-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:25:51.815+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:25:51.816+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01bh9jebrzcxniiqphm4vl3fnr0, remote user nhat.le
2025-07-11T10:25:51.816+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:26:06.844+07:00  INFO 57956 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:26:06.894+07:00  INFO 57956 --- [botTaskExecutor-2] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6181] [company=beehph] type=ZALO for 11/07/2025@10:26:06+0700
2025-07-11T10:26:06.894+07:00  INFO 57956 --- [botTaskExecutor-2] c.d.f.s.message.MessageQueueManager      : Added message [6181] - scheduled at 11/07/2025@10:26:06+0700 - current session (11/07/2025@10:15:00+0700 to 11/07/2025@10:30:00+0700)

2025-07-11T10:26:21.877+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:26:21.879+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01bh9jebrzcxniiqphm4vl3fnr0, remote user nhat.le
2025-07-11T10:26:21.879+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:26:47.715+07:00 DEBUG 57956 --- [qtp1002973859-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:26:49.933+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:26:49.933+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01bh9jebrzcxniiqphm4vl3fnr0, remote user nhat.le
2025-07-11T10:26:49.934+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:27:03.956+07:00  INFO 57956 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:27:04.003+07:00  INFO 57956 --- [botTaskExecutor-3] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6182] [company=beehph] type=ZALO for 11/07/2025@10:27:03+0700
2025-07-11T10:27:04.003+07:00  INFO 57956 --- [botTaskExecutor-3] c.d.f.s.message.MessageQueueManager      : Added message [6182] - scheduled at 11/07/2025@10:27:03+0700 - current session (11/07/2025@10:15:00+0700 to 11/07/2025@10:30:00+0700)

2025-07-11T10:27:17.989+07:00  INFO 57956 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T10:27:18.006+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:27:18.007+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01bh9jebrzcxniiqphm4vl3fnr0, remote user nhat.le
2025-07-11T10:27:18.007+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:27:18.009+07:00  INFO 57956 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:27:47.644+07:00 DEBUG 57956 --- [qtp1002973859-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:27:52.067+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:27:52.067+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01bh9jebrzcxniiqphm4vl3fnr0, remote user nhat.le
2025-07-11T10:27:52.068+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:28:06.092+07:00  INFO 57956 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:28:06.145+07:00  INFO 57956 --- [botTaskExecutor-1] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6183] [company=beehph] type=ZALO for 11/07/2025@10:28:06+0700
2025-07-11T10:28:06.145+07:00  INFO 57956 --- [botTaskExecutor-1] c.d.f.s.message.MessageQueueManager      : Added message [6183] - scheduled at 11/07/2025@10:28:06+0700 - current session (11/07/2025@10:15:00+0700 to 11/07/2025@10:30:00+0700)

2025-07-11T10:28:21.128+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:28:21.130+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01bh9jebrzcxniiqphm4vl3fnr0, remote user nhat.le
2025-07-11T10:28:21.130+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:28:47.618+07:00 DEBUG 57956 --- [qtp1002973859-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:28:49.180+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:28:49.181+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01bh9jebrzcxniiqphm4vl3fnr0, remote user nhat.le
2025-07-11T10:28:49.181+07:00 DEBUG 57956 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:28:52.454+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@6d36c0cc{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-11T10:28:52.455+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-11T10:28:52.455+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-11T10:28:52.455+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-11T10:28:52.456+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-11T10:28:52.456+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-11T10:28:52.456+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-11T10:28:52.456+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-11T10:28:52.456+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-11T10:28:52.456+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-11T10:28:52.456+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-11T10:28:52.456+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-11T10:28:52.456+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-11T10:28:52.456+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-11T10:28:52.456+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-11T10:28:52.456+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-11T10:28:52.471+07:00  INFO 57956 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:28:52.535+07:00  INFO 57956 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-11T10:28:52.540+07:00  INFO 57956 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-11T10:28:52.563+07:00  INFO 57956 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T10:28:52.565+07:00  INFO 57956 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T10:28:52.566+07:00  INFO 57956 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-11T10:28:52.567+07:00  INFO 57956 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-11T10:28:52.567+07:00  INFO 57956 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-11T10:28:52.718+07:00  INFO 57956 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-11T10:28:52.718+07:00  INFO 57956 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-11T10:28:52.719+07:00  INFO 57956 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-11T10:28:52.719+07:00  INFO 57956 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-11T10:28:52.720+07:00  INFO 57956 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-11T10:28:52.724+07:00  INFO 57956 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@2810d9{STOPPING}[12.0.15,sto=0]
2025-07-11T10:28:52.726+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-11T10:28:52.728+07:00  INFO 57956 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@7cc742d{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13082278128195206837/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@22bf2a0d{STOPPED}}
