2025-07-11T15:29:56.612+07:00  INFO 94209 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 94209 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-11T15:29:56.613+07:00  INFO 94209 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data"
2025-07-11T15:29:57.352+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.413+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 57 ms. Found 22 JPA repository interfaces.
2025-07-11T15:29:57.423+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.424+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-11T15:29:57.424+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.467+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 42 ms. Found 9 JPA repository interfaces.
2025-07-11T15:29:57.468+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.470+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-11T15:29:57.470+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.473+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T15:29:57.484+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.489+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-11T15:29:57.499+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.503+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-07-11T15:29:57.506+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.508+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 3 JPA repository interfaces.
2025-07-11T15:29:57.508+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.509+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T15:29:57.513+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.519+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-11T15:29:57.523+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.526+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-11T15:29:57.526+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.529+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T15:29:57.530+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.537+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-11T15:29:57.537+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.539+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-11T15:29:57.540+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.540+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T15:29:57.540+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.541+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-07-11T15:29:57.541+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.544+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-11T15:29:57.544+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.546+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-11T15:29:57.546+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.546+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T15:29:57.546+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.556+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-07-11T15:29:57.565+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.571+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-07-11T15:29:57.573+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.576+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-11T15:29:57.576+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.580+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-11T15:29:57.580+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.585+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-11T15:29:57.586+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.589+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T15:29:57.589+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.592+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-11T15:29:57.592+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.600+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-07-11T15:29:57.601+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.615+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 25 JPA repository interfaces.
2025-07-11T15:29:57.628+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.639+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-07-11T15:29:57.639+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.644+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-11T15:29:57.644+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.646+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-11T15:29:57.650+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.651+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T15:29:57.651+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.657+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-11T15:29:57.661+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.697+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 67 JPA repository interfaces.
2025-07-11T15:29:57.697+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.698+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-11T15:29:57.699+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T15:29:57.701+07:00  INFO 94209 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-11T15:29:57.883+07:00  INFO 94209 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-11T15:29:57.886+07:00  INFO 94209 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-11T15:29:58.146+07:00  WARN 94209 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-11T15:29:58.343+07:00  INFO 94209 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-11T15:29:58.345+07:00  INFO 94209 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-11T15:29:58.357+07:00  INFO 94209 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-11T15:29:58.357+07:00  INFO 94209 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1622 ms
2025-07-11T15:29:58.411+07:00  WARN 94209 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T15:29:58.411+07:00  INFO 94209 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-11T15:29:58.504+07:00  INFO 94209 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@1b1d2d7b
2025-07-11T15:29:58.505+07:00  INFO 94209 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-11T15:29:58.510+07:00  WARN 94209 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T15:29:58.510+07:00  INFO 94209 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-11T15:29:58.516+07:00  INFO 94209 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@298154d4
2025-07-11T15:29:58.516+07:00  INFO 94209 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-11T15:29:58.516+07:00  WARN 94209 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T15:29:58.516+07:00  INFO 94209 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-11T15:29:59.002+07:00  INFO 94209 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2cf073da
2025-07-11T15:29:59.003+07:00  INFO 94209 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-11T15:29:59.003+07:00  WARN 94209 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T15:29:59.003+07:00  INFO 94209 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-11T15:29:59.015+07:00  INFO 94209 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@3d26c82d
2025-07-11T15:29:59.015+07:00  INFO 94209 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-11T15:29:59.015+07:00  INFO 94209 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-11T15:29:59.064+07:00  INFO 94209 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-11T15:29:59.066+07:00  INFO 94209 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@50626d22{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5841096934721349836/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@734db023{STARTED}}
2025-07-11T15:29:59.066+07:00  INFO 94209 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@50626d22{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5841096934721349836/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@734db023{STARTED}}
2025-07-11T15:29:59.068+07:00  INFO 94209 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@6d985720{STARTING}[12.0.15,sto=0] @2997ms
2025-07-11T15:29:59.118+07:00  INFO 94209 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11T15:29:59.145+07:00  INFO 94209 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-11T15:29:59.159+07:00  INFO 94209 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-11T15:29:59.277+07:00  INFO 94209 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-11T15:29:59.307+07:00  WARN 94209 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-11T15:29:59.891+07:00  INFO 94209 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-11T15:29:59.899+07:00  INFO 94209 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@5facf061] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-11T15:30:00.011+07:00  INFO 94209 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T15:30:00.187+07:00  INFO 94209 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "cloud.datatp.fforwarder.price", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-07-11T15:30:00.189+07:00  INFO 94209 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-11T15:30:00.197+07:00  INFO 94209 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11T15:30:00.198+07:00  INFO 94209 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-11T15:30:00.227+07:00  INFO 94209 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-11T15:30:00.230+07:00  WARN 94209 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-11T15:30:03.715+07:00  INFO 94209 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-11T15:30:03.735+07:00  INFO 94209 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@10af83ad] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-11T15:30:04.142+07:00  WARN 94209 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-07-11T15:30:04.142+07:00  WARN 94209 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-07-11T15:30:04.150+07:00  WARN 94209 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-07-11T15:30:04.150+07:00  WARN 94209 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-07-11T15:30:04.172+07:00  WARN 94209 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-07-11T15:30:04.173+07:00  WARN 94209 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-07-11T15:30:05.207+07:00  INFO 94209 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T15:30:05.264+07:00  INFO 94209 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-11T15:30:05.272+07:00  INFO 94209 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-11T15:30:05.272+07:00  INFO 94209 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-11T15:30:05.282+07:00  WARN 94209 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-11T15:30:05.450+07:00  INFO 94209 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-11T15:30:06.026+07:00  INFO 94209 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-11T15:30:06.030+07:00  INFO 94209 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-11T15:30:06.070+07:00  INFO 94209 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-11T15:30:06.119+07:00  INFO 94209 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-11T15:30:06.212+07:00  INFO 94209 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-11T15:30:06.248+07:00  INFO 94209 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-11T15:30:06.272+07:00  INFO 94209 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 16003030ms : this is harmless.
2025-07-11T15:30:06.282+07:00  INFO 94209 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-11T15:30:06.286+07:00  INFO 94209 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-11T15:30:06.300+07:00  INFO 94209 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 22099236ms : this is harmless.
2025-07-11T15:30:06.302+07:00  INFO 94209 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-11T15:30:06.440+07:00  INFO 94209 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-11T15:30:06.441+07:00  INFO 94209 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-11T15:30:09.303+07:00  INFO 94209 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 1 messages for session 11/07/2025@15:30:00+0700 to 11/07/2025@15:45:00+0700
2025-07-11T15:30:09.303+07:00  INFO 94209 --- [main] c.d.f.s.message.MessageQueueManager      : 📤 message : MAIL - inquiry-overdue-reminder - scheduled at 11/07/2025@15:31:04+0700
2025-07-11T15:30:09.303+07:00  INFO 94209 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 1 messages for session 11/07/2025@15:30:00+0700 to 11/07/2025@15:45:00+0700
2025-07-11T15:30:10.603+07:00  INFO 94209 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-11T15:30:10.603+07:00  INFO 94209 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-11T15:30:10.604+07:00  WARN 94209 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-11T15:30:10.937+07:00  INFO 94209 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-11T15:30:10.937+07:00  INFO 94209 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-11T15:30:10.937+07:00  INFO 94209 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-11T15:30:10.937+07:00  INFO 94209 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-11T15:30:10.937+07:00  INFO 94209 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-11T15:30:13.048+07:00  WARN 94209 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 1453e698-b8de-4d6c-b17e-b3355a6bb240

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-11T15:30:13.053+07:00  INFO 94209 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-11T15:30:13.409+07:00  INFO 94209 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-11T15:30:13.409+07:00  INFO 94209 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-11T15:30:13.409+07:00  INFO 94209 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-11T15:30:13.409+07:00  INFO 94209 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-11T15:30:13.409+07:00  INFO 94209 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-11T15:30:13.409+07:00  INFO 94209 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-11T15:30:13.409+07:00  INFO 94209 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-11T15:30:13.409+07:00  INFO 94209 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-11T15:30:13.409+07:00  INFO 94209 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-11T15:30:13.409+07:00  INFO 94209 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-11T15:30:13.409+07:00  INFO 94209 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-11T15:30:13.409+07:00  INFO 94209 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-11T15:30:13.413+07:00  INFO 94209 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-11T15:30:13.413+07:00  INFO 94209 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-11T15:30:13.413+07:00  INFO 94209 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-11T15:30:13.472+07:00  INFO 94209 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11T15:30:13.472+07:00  INFO 94209 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-11T15:30:13.473+07:00  INFO 94209 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-11T15:30:13.483+07:00  INFO 94209 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@34d9c9fe{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-11T15:30:13.484+07:00  INFO 94209 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-11T15:30:13.485+07:00  INFO 94209 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-11T15:30:13.547+07:00  INFO 94209 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-11T15:30:13.547+07:00  INFO 94209 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-11T15:30:13.553+07:00  INFO 94209 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 17.205 seconds (process running for 17.482)
2025-07-11T15:30:22.788+07:00  INFO 94209 --- [qtp1993997862-37] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-11T15:30:26.729+07:00  INFO 94209 --- [qtp1993997862-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node04ugwvk43uq1hmjvn8kum1f580
2025-07-11T15:30:26.898+07:00  INFO 94209 --- [qtp1993997862-40] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = 12ba31d6838d9867f446c70655c97d7c
2025-07-11T15:30:27.268+07:00  INFO 94209 --- [qtp1993997862-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T15:30:29.313+07:00 DEBUG 94209 --- [qtp1993997862-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T15:30:29.314+07:00 DEBUG 94209 --- [qtp1993997862-59] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T15:30:33.061+07:00  INFO 94209 --- [qtp1993997862-59] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = 12ba31d6838d9867f446c70655c97d7c
2025-07-11T15:30:33.062+07:00  INFO 94209 --- [qtp1993997862-37] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = 12ba31d6838d9867f446c70655c97d7c
2025-07-11T15:30:33.073+07:00  INFO 94209 --- [qtp1993997862-59] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T15:30:33.076+07:00  INFO 94209 --- [qtp1993997862-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T15:30:34.303+07:00 DEBUG 94209 --- [qtp1993997862-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T15:30:34.306+07:00 DEBUG 94209 --- [qtp1993997862-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T15:31:02.514+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:31:16.579+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-07-11T15:31:16.608+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:31:16.608+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user nhat.le
2025-07-11T15:31:16.608+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:31:16.612+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T15:31:25.743+07:00  INFO 94209 --- [qtp1993997862-35] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = 12ba31d6838d9867f446c70655c97d7c
2025-07-11T15:31:25.743+07:00  INFO 94209 --- [qtp1993997862-34] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = 12ba31d6838d9867f446c70655c97d7c
2025-07-11T15:31:25.753+07:00  INFO 94209 --- [qtp1993997862-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T15:31:25.755+07:00  INFO 94209 --- [qtp1993997862-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T15:31:28.800+07:00 DEBUG 94209 --- [qtp1993997862-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T15:31:28.800+07:00 DEBUG 94209 --- [qtp1993997862-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T15:31:29.527+07:00  INFO 94209 --- [qtp1993997862-37] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = 12ba31d6838d9867f446c70655c97d7c
2025-07-11T15:31:29.545+07:00  INFO 94209 --- [qtp1993997862-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T15:31:29.585+07:00  INFO 94209 --- [qtp1993997862-58] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = 12ba31d6838d9867f446c70655c97d7c
2025-07-11T15:31:29.586+07:00 ERROR 94209 --- [qtp1993997862-37] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getCode()" because "company" is null
	at net.datatp.module.core.security.http.RPCController$1.preCall(RPCController.java:91)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:137)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:104)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:51)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-07-11T15:31:29.592+07:00 ERROR 94209 --- [qtp1993997862-61] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getCode()" because "company" is null
	at net.datatp.module.core.security.http.RPCController$1.preCall(RPCController.java:91)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:137)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:104)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:51)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-07-11T15:31:29.586+07:00 ERROR 94209 --- [qtp1993997862-36] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getCode()" because "company" is null
	at net.datatp.module.core.security.http.RPCController$1.preCall(RPCController.java:91)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:137)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:104)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:51)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-07-11T15:31:29.597+07:00  INFO 94209 --- [qtp1993997862-36] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint SecurityService/findApps
2025-07-11T15:31:29.597+07:00  INFO 94209 --- [qtp1993997862-61] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint SecurityService/findApps
2025-07-11T15:31:29.595+07:00  INFO 94209 --- [qtp1993997862-37] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint ChatService/searchChatMessageHistory
2025-07-11T15:31:29.587+07:00 ERROR 94209 --- [qtp1993997862-59] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getCode()" because "company" is null
	at net.datatp.module.core.security.http.RPCController$1.preCall(RPCController.java:91)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:137)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:104)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:51)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-07-11T15:31:29.598+07:00  INFO 94209 --- [qtp1993997862-59] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint ChatService/searchChatMessageHistory
2025-07-11T15:31:29.600+07:00  INFO 94209 --- [qtp1993997862-58] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T15:31:30.588+07:00 DEBUG 94209 --- [qtp1993997862-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T15:31:30.588+07:00 DEBUG 94209 --- [qtp1993997862-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T15:31:33.764+07:00  INFO 94209 --- [qtp1993997862-34] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = 12ba31d6838d9867f446c70655c97d7c
2025-07-11T15:31:33.765+07:00  INFO 94209 --- [qtp1993997862-58] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = 12ba31d6838d9867f446c70655c97d7c
2025-07-11T15:31:33.778+07:00  INFO 94209 --- [qtp1993997862-58] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T15:31:33.778+07:00  INFO 94209 --- [qtp1993997862-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T15:31:36.296+07:00 DEBUG 94209 --- [qtp1993997862-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T15:31:36.298+07:00 DEBUG 94209 --- [qtp1993997862-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T15:31:49.673+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:31:49.673+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user nhat.le
2025-07-11T15:31:49.673+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:32:00.229+07:00  INFO 94209 --- [qtp1993997862-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-07-11T15:32:03.280+07:00  INFO 94209 --- [qtp1993997862-34] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = 5f9155f575e1217ac5d21e4112764207
2025-07-11T15:32:03.286+07:00  INFO 94209 --- [qtp1993997862-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T15:32:04.317+07:00 DEBUG 94209 --- [qtp1993997862-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:32:04.317+07:00 DEBUG 94209 --- [qtp1993997862-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:32:05.702+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:32:19.721+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:32:19.723+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:32:19.723+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:32:24.186+07:00  INFO 94209 --- [qtp1993997862-36] n.d.m.c.a.CompanyAuthenticationService   : User dan logout successfully 
2025-07-11T15:32:25.841+07:00  INFO 94209 --- [qtp1993997862-41] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = 30f12683738f31758112f578837a4fc2
2025-07-11T15:32:25.848+07:00  INFO 94209 --- [qtp1993997862-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T15:32:27.567+07:00 DEBUG 94209 --- [qtp1993997862-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T15:32:27.567+07:00 DEBUG 94209 --- [qtp1993997862-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T15:32:34.653+07:00  INFO 94209 --- [qtp1993997862-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-07-11T15:32:37.796+07:00  INFO 94209 --- [qtp1993997862-60] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T15:32:37.803+07:00  INFO 94209 --- [qtp1993997862-60] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T15:32:38.825+07:00 DEBUG 94209 --- [qtp1993997862-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:32:38.825+07:00 DEBUG 94209 --- [qtp1993997862-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:32:47.770+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:32:47.771+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:32:47.771+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:33:06.800+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:33:15.871+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 24, expire count 0
2025-07-11T15:33:15.896+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:33:15.897+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:33:15.897+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:33:15.903+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 2, expire count 1
2025-07-11T15:33:38.829+07:00 DEBUG 94209 --- [qtp1993997862-59] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:33:49.965+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:33:49.966+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:33:49.966+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:34:05.006+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:34:19.032+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:34:19.032+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:34:19.032+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:34:38.841+07:00 DEBUG 94209 --- [qtp1993997862-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:34:47.072+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:34:47.073+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:34:47.074+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:35:06.098+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:35:06.101+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T15:35:20.148+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 14, expire count 0
2025-07-11T15:35:20.161+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:35:20.161+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:35:20.161+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:35:20.161+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T15:35:38.851+07:00 DEBUG 94209 --- [qtp1993997862-59] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:35:50.215+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:35:50.217+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:35:50.217+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:36:04.236+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:36:18.263+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:36:18.264+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:36:18.264+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:36:38.862+07:00 DEBUG 94209 --- [qtp1993997862-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:36:46.307+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:36:46.308+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:36:46.308+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:37:06.343+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:37:20.375+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T15:37:20.385+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:37:20.385+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:37:20.385+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:37:20.386+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-07-11T15:37:38.846+07:00 DEBUG 94209 --- [qtp1993997862-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:37:49.439+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:37:49.441+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:37:49.445+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:38:03.469+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:38:17.493+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:38:17.495+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:38:17.495+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:38:38.865+07:00 DEBUG 94209 --- [qtp1993997862-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:38:50.545+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:38:50.546+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:38:50.547+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:39:06.581+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:39:19.609+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T15:39:19.614+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:39:19.615+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:39:19.615+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:39:19.615+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T15:39:39.708+07:00 DEBUG 94209 --- [qtp1993997862-59] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:39:48.666+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:39:48.668+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:39:48.668+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:40:02.693+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:40:02.698+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T15:40:16.719+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:40:16.719+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:40:16.720+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:40:49.766+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:40:49.768+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:40:49.768+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:41:05.803+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:41:17.218+07:00 DEBUG 94209 --- [qtp1993997862-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:41:19.835+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 9
2025-07-11T15:41:19.841+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:41:19.842+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:41:19.842+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:41:19.842+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T15:41:38.850+07:00 DEBUG 94209 --- [qtp1993997862-68] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:41:47.889+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:41:47.891+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:41:47.891+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:42:06.914+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:42:15.932+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:42:15.932+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:42:15.933+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:42:38.848+07:00 DEBUG 94209 --- [qtp1993997862-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:42:49.994+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:42:49.994+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:42:49.994+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:43:05.020+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:43:19.065+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 29
2025-07-11T15:43:19.080+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:43:19.080+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:43:19.080+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:43:19.086+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T15:43:38.843+07:00 DEBUG 94209 --- [qtp1993997862-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:43:47.135+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:43:47.135+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:43:47.136+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:44:06.158+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:44:20.190+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:44:20.190+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:44:20.191+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:44:38.849+07:00 DEBUG 94209 --- [qtp1993997862-66] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:44:50.250+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:44:50.251+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:44:50.251+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:45:04.273+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:45:04.275+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T15:45:04.276+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-11T15:45:04.276+07:00  INFO 94209 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 11/07/2025@15:45:04+0700
2025-07-11T15:45:04.301+07:00  INFO 94209 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 11/07/2025@15:45:00+0700 to 11/07/2025@16:00:00+0700
2025-07-11T15:45:04.301+07:00  INFO 94209 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 11/07/2025@15:45:00+0700 to 11/07/2025@16:00:00+0700
2025-07-11T15:45:10.337+07:00 DEBUG 94209 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "11/07/2025 00:00:00 GMT+0700",
      "toDate" : "11/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-11T15:45:18.352+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-07-11T15:45:18.356+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:45:18.356+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:45:18.356+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:45:18.356+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T15:45:38.847+07:00 DEBUG 94209 --- [qtp1993997862-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:45:46.397+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:45:46.397+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:45:46.398+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:46:06.430+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:46:20.451+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:46:20.452+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:46:20.453+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:46:38.877+07:00 DEBUG 94209 --- [qtp1993997862-68] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:46:49.504+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:46:49.506+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:46:49.507+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:47:03.529+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:47:17.552+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T15:47:17.553+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:47:17.554+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:47:17.554+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:47:17.554+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T15:47:39.724+07:00 DEBUG 94209 --- [qtp1993997862-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:47:45.601+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:47:45.612+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:47:45.614+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:48:06.649+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:48:19.680+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:48:19.691+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:48:19.691+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:48:48.744+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:48:48.746+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:48:48.746+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:49:02.770+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:49:16.893+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T15:49:16.911+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:49:16.912+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:49:16.912+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:49:16.915+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T15:49:22.774+07:00 DEBUG 94209 --- [qtp1993997862-66] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:49:49.962+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:49:49.964+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:49:49.964+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:49:59.553+07:00 DEBUG 94209 --- [qtp1993997862-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:50:05.986+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:50:05.989+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T15:50:20.008+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:50:20.009+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:50:20.009+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:50:38.901+07:00 DEBUG 94209 --- [qtp1993997862-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:50:48.050+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:50:48.050+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:50:48.050+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:51:02.070+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:51:16.123+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-07-11T15:51:16.132+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:51:16.132+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:51:16.132+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:51:16.135+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T15:51:39.768+07:00 DEBUG 94209 --- [qtp1993997862-68] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:51:50.183+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:51:50.183+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:51:50.184+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:52:05.204+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:52:19.231+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:52:19.232+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:52:19.232+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:52:47.283+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:52:47.285+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:52:47.285+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:52:55.677+07:00 DEBUG 94209 --- [qtp1993997862-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:53:06.317+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:53:20.358+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 4
2025-07-11T15:53:20.377+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:53:20.377+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:53:20.378+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:53:20.379+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T15:53:38.951+07:00 DEBUG 94209 --- [qtp1993997862-111] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:53:50.443+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:53:50.445+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:53:50.445+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:54:04.463+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:54:18.486+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:54:18.488+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:54:18.488+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:54:38.926+07:00 DEBUG 94209 --- [qtp1993997862-108] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:54:46.537+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:54:46.539+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:54:46.539+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:55:06.568+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:55:06.572+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T15:55:20.602+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T15:55:20.609+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:55:20.610+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:55:20.610+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:55:20.610+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T15:55:38.941+07:00 DEBUG 94209 --- [qtp1993997862-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:55:49.670+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:55:49.671+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:55:49.671+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:55:55.005+07:00  INFO 94209 --- [qtp1993997862-34] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T15:55:55.006+07:00  INFO 94209 --- [qtp1993997862-35] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T15:55:55.028+07:00  INFO 94209 --- [qtp1993997862-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T15:55:55.028+07:00  INFO 94209 --- [qtp1993997862-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T15:55:56.079+07:00 DEBUG 94209 --- [qtp1993997862-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:55:56.079+07:00 DEBUG 94209 --- [qtp1993997862-66] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:56:03.688+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:56:17.721+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:56:17.722+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:56:17.722+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:56:45.770+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:56:45.772+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:56:45.772+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:56:56.082+07:00 DEBUG 94209 --- [qtp1993997862-108] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:57:06.800+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:57:19.847+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-07-11T15:57:19.859+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:57:19.859+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:57:19.859+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:57:19.861+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T15:57:48.929+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:57:48.932+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:57:48.932+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:57:56.166+07:00 DEBUG 94209 --- [qtp1993997862-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:58:02.956+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:58:16.972+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:58:16.973+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:58:16.973+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:58:50.029+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:58:50.032+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:58:50.033+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:58:56.143+07:00 DEBUG 94209 --- [qtp1993997862-116] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:59:06.064+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T15:59:20.105+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-11T15:59:20.111+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:59:20.112+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:59:20.112+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T15:59:20.113+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T15:59:46.551+07:00  INFO 94209 --- [qtp1993997862-35] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T15:59:46.572+07:00  INFO 94209 --- [qtp1993997862-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T15:59:46.582+07:00  INFO 94209 --- [qtp1993997862-116] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T15:59:46.592+07:00  INFO 94209 --- [qtp1993997862-116] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T15:59:47.614+07:00 DEBUG 94209 --- [qtp1993997862-66] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:59:47.615+07:00 DEBUG 94209 --- [qtp1993997862-116] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T15:59:48.158+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T15:59:48.158+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T15:59:48.159+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:00:02.177+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:00:02.180+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T16:00:02.180+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-11T16:00:02.181+07:00  INFO 94209 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 11/07/2025@16:00:02+0700
2025-07-11T16:00:02.195+07:00  INFO 94209 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 11/07/2025@16:00:00+0700 to 11/07/2025@16:15:00+0700
2025-07-11T16:00:02.196+07:00  INFO 94209 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 11/07/2025@16:00:00+0700 to 11/07/2025@16:15:00+0700
2025-07-11T16:00:02.198+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-07-11T16:00:08.210+07:00 DEBUG 94209 --- [botTaskExecutor-3] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "11/07/2025 00:00:00 GMT+0700",
      "toDate" : "11/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-11T16:00:16.232+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:00:16.233+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:00:16.233+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:00:26.372+07:00  INFO 94209 --- [qtp1993997862-35] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:00:26.392+07:00  INFO 94209 --- [qtp1993997862-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:00:26.734+07:00  INFO 94209 --- [qtp1993997862-108] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:00:26.746+07:00  INFO 94209 --- [qtp1993997862-108] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:00:27.775+07:00 DEBUG 94209 --- [qtp1993997862-108] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:00:27.775+07:00 DEBUG 94209 --- [qtp1993997862-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:00:50.286+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:00:50.287+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:00:50.288+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:01:05.316+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:01:17.057+07:00  INFO 94209 --- [qtp1993997862-108] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:01:17.083+07:00  INFO 94209 --- [qtp1993997862-66] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:01:17.131+07:00  INFO 94209 --- [qtp1993997862-108] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:01:17.131+07:00  INFO 94209 --- [qtp1993997862-66] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:01:18.177+07:00 DEBUG 94209 --- [qtp1993997862-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:01:18.177+07:00 DEBUG 94209 --- [qtp1993997862-116] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:01:19.404+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-07-11T16:01:19.410+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:01:19.412+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:01:19.413+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:01:19.418+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T16:01:23.825+07:00  INFO 94209 --- [qtp1993997862-116] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:01:23.858+07:00  INFO 94209 --- [qtp1993997862-118] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:01:23.864+07:00  INFO 94209 --- [qtp1993997862-116] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:01:23.874+07:00  INFO 94209 --- [qtp1993997862-118] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:01:24.909+07:00 DEBUG 94209 --- [qtp1993997862-116] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:01:24.910+07:00 DEBUG 94209 --- [qtp1993997862-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:01:47.462+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:01:47.465+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:01:47.466+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:01:59.963+07:00 DEBUG 94209 --- [qtp1993997862-141] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:02:00.054+07:00 DEBUG 94209 --- [qtp1993997862-141] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:02:06.491+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:02:20.513+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:02:20.514+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:02:20.514+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:02:49.633+07:00  INFO 94209 --- [qtp1993997862-35] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:02:49.634+07:00  INFO 94209 --- [qtp1993997862-34] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:02:49.644+07:00  INFO 94209 --- [qtp1993997862-35] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:02:49.644+07:00  INFO 94209 --- [qtp1993997862-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:02:50.589+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:02:50.590+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:02:50.590+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:02:50.693+07:00 DEBUG 94209 --- [qtp1993997862-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:02:50.693+07:00 DEBUG 94209 --- [qtp1993997862-66] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:03:04.628+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:03:06.017+07:00  INFO 94209 --- [qtp1993997862-34] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:03:06.031+07:00  INFO 94209 --- [qtp1993997862-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:03:06.048+07:00  INFO 94209 --- [qtp1993997862-119] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:03:06.059+07:00  INFO 94209 --- [qtp1993997862-119] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:03:07.063+07:00 DEBUG 94209 --- [qtp1993997862-141] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:03:07.064+07:00 DEBUG 94209 --- [qtp1993997862-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:03:18.672+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-11T16:03:18.679+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:03:18.679+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:03:18.679+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:03:18.680+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T16:03:46.718+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:03:46.721+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:03:46.722+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:04:06.758+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:04:07.088+07:00 DEBUG 94209 --- [qtp1993997862-93] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:04:19.777+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:04:19.778+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:04:19.778+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:04:20.400+07:00  INFO 94209 --- [qtp1993997862-118] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:04:20.413+07:00  INFO 94209 --- [qtp1993997862-118] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:04:20.426+07:00  INFO 94209 --- [qtp1993997862-93] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:04:20.430+07:00  INFO 94209 --- [qtp1993997862-93] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:04:21.456+07:00 DEBUG 94209 --- [qtp1993997862-66] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:04:21.457+07:00 DEBUG 94209 --- [qtp1993997862-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:04:49.834+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:04:49.836+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:04:49.837+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:04:57.581+07:00  INFO 94209 --- [qtp1993997862-66] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:04:57.600+07:00  INFO 94209 --- [qtp1993997862-66] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:04:57.607+07:00  INFO 94209 --- [qtp1993997862-141] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:04:57.617+07:00  INFO 94209 --- [qtp1993997862-141] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:04:58.641+07:00 DEBUG 94209 --- [qtp1993997862-145] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:04:58.642+07:00 DEBUG 94209 --- [qtp1993997862-118] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:05:03.864+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:05:03.867+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T16:05:17.924+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-07-11T16:05:17.940+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:05:17.941+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:05:17.942+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:05:17.946+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T16:05:45.996+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:05:45.998+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:05:45.998+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:05:49.060+07:00  INFO 94209 --- [qtp1993997862-141] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:05:49.062+07:00  INFO 94209 --- [qtp1993997862-118] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:05:49.074+07:00  INFO 94209 --- [qtp1993997862-141] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:05:49.074+07:00  INFO 94209 --- [qtp1993997862-118] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:05:50.110+07:00 DEBUG 94209 --- [qtp1993997862-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:05:50.112+07:00 DEBUG 94209 --- [qtp1993997862-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:06:06.024+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:06:06.645+07:00  INFO 94209 --- [qtp1993997862-34] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:06:06.647+07:00  INFO 94209 --- [qtp1993997862-58] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:06:06.657+07:00  INFO 94209 --- [qtp1993997862-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:06:06.662+07:00  INFO 94209 --- [qtp1993997862-58] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:06:07.704+07:00 DEBUG 94209 --- [qtp1993997862-66] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:06:07.704+07:00 DEBUG 94209 --- [qtp1993997862-118] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:06:20.045+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:06:20.048+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:06:20.049+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:06:49.086+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:06:49.088+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:06:49.088+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:06:51.041+07:00  INFO 94209 --- [qtp1993997862-118] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:06:51.042+07:00  INFO 94209 --- [qtp1993997862-93] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:06:51.055+07:00  INFO 94209 --- [qtp1993997862-118] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:06:51.055+07:00  INFO 94209 --- [qtp1993997862-93] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:06:52.098+07:00 DEBUG 94209 --- [qtp1993997862-141] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:06:52.098+07:00 DEBUG 94209 --- [qtp1993997862-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:07:03.115+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:07:17.143+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 1
2025-07-11T16:07:17.148+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:07:17.148+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:07:17.148+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:07:17.148+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T16:07:20.181+07:00  INFO 94209 --- [qtp1993997862-118] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:07:20.181+07:00  INFO 94209 --- [qtp1993997862-93] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:07:20.191+07:00  INFO 94209 --- [qtp1993997862-93] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:07:20.191+07:00  INFO 94209 --- [qtp1993997862-118] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:07:21.228+07:00 DEBUG 94209 --- [qtp1993997862-66] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:07:21.228+07:00 DEBUG 94209 --- [qtp1993997862-118] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:07:50.199+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:07:50.201+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:07:50.202+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:08:06.237+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:08:20.264+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:08:20.265+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:08:20.265+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:08:21.263+07:00 DEBUG 94209 --- [qtp1993997862-142] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:08:48.315+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:08:48.319+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:08:48.320+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:09:02.341+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:09:16.382+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-07-11T16:09:16.392+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:09:16.392+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:09:16.393+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:09:16.396+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T16:09:21.258+07:00 DEBUG 94209 --- [qtp1993997862-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:09:50.443+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:09:50.444+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:09:50.444+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:10:05.480+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T16:10:05.482+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:10:19.508+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:10:19.511+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:10:19.512+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:10:21.254+07:00 DEBUG 94209 --- [qtp1993997862-142] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:10:47.565+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:10:47.572+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:10:47.573+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:11:06.602+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:11:20.654+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 1
2025-07-11T16:11:20.661+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:11:20.661+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:11:20.661+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:11:20.662+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T16:11:21.253+07:00 DEBUG 94209 --- [qtp1993997862-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:11:49.720+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:11:49.724+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:11:49.724+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:12:04.746+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:12:18.773+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:12:18.776+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:12:18.776+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:12:21.256+07:00 DEBUG 94209 --- [qtp1993997862-142] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:12:46.819+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:12:46.820+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:12:46.820+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:13:06.854+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:13:19.883+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T16:13:19.886+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:13:19.886+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:13:19.886+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:13:19.887+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T16:13:21.259+07:00 DEBUG 94209 --- [qtp1993997862-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:13:49.937+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:13:49.940+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:13:49.940+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:14:03.960+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:14:17.992+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:14:17.993+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:14:17.994+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:14:21.269+07:00 DEBUG 94209 --- [qtp1993997862-142] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:14:46.032+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:14:46.034+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:14:46.035+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:15:06.067+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-11T16:15:06.071+07:00  INFO 94209 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 11/07/2025@16:15:06+0700
2025-07-11T16:15:06.099+07:00  INFO 94209 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 11/07/2025@16:15:00+0700 to 11/07/2025@16:30:00+0700
2025-07-11T16:15:06.100+07:00  INFO 94209 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 11/07/2025@16:15:00+0700 to 11/07/2025@16:30:00+0700
2025-07-11T16:15:06.100+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:15:06.101+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T16:15:06.106+07:00 DEBUG 94209 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "11/07/2025 00:00:00 GMT+0700",
      "toDate" : "11/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-11T16:15:20.143+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-11T16:15:20.146+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:15:20.147+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:15:20.147+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:15:20.147+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T16:15:21.260+07:00 DEBUG 94209 --- [qtp1993997862-34] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:15:49.201+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:15:49.202+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:15:49.203+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:16:03.231+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:16:17.260+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:16:17.262+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:16:17.263+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:16:21.280+07:00 DEBUG 94209 --- [qtp1993997862-118] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:16:50.310+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:16:50.313+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:16:50.314+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:17:06.343+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:17:20.391+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-07-11T16:17:20.400+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:17:20.401+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:17:20.401+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:17:20.401+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T16:17:21.276+07:00 DEBUG 94209 --- [qtp1993997862-66] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:17:48.446+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:17:48.446+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:17:48.446+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:18:02.483+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:18:15.224+07:00  INFO 94209 --- [qtp1993997862-66] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:18:15.429+07:00  INFO 94209 --- [qtp1993997862-142] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:18:15.448+07:00  INFO 94209 --- [qtp1993997862-66] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:18:15.460+07:00  INFO 94209 --- [qtp1993997862-142] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:18:16.540+07:00 DEBUG 94209 --- [qtp1993997862-149] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:18:16.577+07:00 DEBUG 94209 --- [qtp1993997862-184] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:18:16.594+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:18:16.594+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:18:16.594+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:18:49.646+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:18:49.671+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:18:49.671+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:19:05.700+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:19:16.547+07:00 DEBUG 94209 --- [qtp1993997862-184] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:19:19.748+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-07-11T16:19:19.758+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:19:19.758+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:19:19.758+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:19:19.764+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T16:19:35.384+07:00  INFO 94209 --- [qtp1993997862-184] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:19:35.388+07:00  INFO 94209 --- [qtp1993997862-186] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:19:35.406+07:00  INFO 94209 --- [qtp1993997862-186] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:19:35.406+07:00  INFO 94209 --- [qtp1993997862-184] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:19:36.704+07:00 DEBUG 94209 --- [qtp1993997862-189] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:19:36.704+07:00 DEBUG 94209 --- [qtp1993997862-191] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:19:47.810+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:19:47.811+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:19:47.812+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:20:06.844+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:20:06.850+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T16:20:15.861+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:20:15.862+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:20:15.862+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:20:36.686+07:00 DEBUG 94209 --- [qtp1993997862-191] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:20:46.435+07:00  INFO 94209 --- [qtp1993997862-184] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:20:46.564+07:00  INFO 94209 --- [qtp1993997862-191] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:20:46.635+07:00  INFO 94209 --- [qtp1993997862-184] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:20:46.674+07:00  INFO 94209 --- [qtp1993997862-191] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:20:47.774+07:00 DEBUG 94209 --- [qtp1993997862-191] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:20:47.775+07:00 DEBUG 94209 --- [qtp1993997862-189] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:20:49.939+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:20:49.939+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:20:49.939+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:20:55.744+07:00  INFO 94209 --- [qtp1993997862-186] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:20:55.814+07:00  INFO 94209 --- [qtp1993997862-149] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:20:55.871+07:00  INFO 94209 --- [qtp1993997862-186] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:20:55.874+07:00  INFO 94209 --- [qtp1993997862-149] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:20:56.918+07:00 DEBUG 94209 --- [qtp1993997862-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:20:56.919+07:00 DEBUG 94209 --- [qtp1993997862-186] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:21:04.999+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:21:19.051+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T16:21:19.057+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:21:19.057+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:21:19.058+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:21:19.059+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T16:21:38.735+07:00  INFO 94209 --- [qtp1993997862-72] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:21:38.741+07:00  INFO 94209 --- [qtp1993997862-183] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:21:38.745+07:00  INFO 94209 --- [qtp1993997862-72] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:21:38.759+07:00  INFO 94209 --- [qtp1993997862-183] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:21:40.313+07:00 DEBUG 94209 --- [qtp1993997862-149] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:21:40.366+07:00 DEBUG 94209 --- [qtp1993997862-190] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:21:43.528+07:00  INFO 94209 --- [qtp1993997862-118] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:21:43.533+07:00  INFO 94209 --- [qtp1993997862-149] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:21:43.685+07:00  INFO 94209 --- [qtp1993997862-118] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:21:43.685+07:00  INFO 94209 --- [qtp1993997862-149] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:21:44.738+07:00 DEBUG 94209 --- [qtp1993997862-191] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:21:44.745+07:00 DEBUG 94209 --- [qtp1993997862-190] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:21:47.560+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:21:47.561+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:21:47.562+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:22:06.593+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:22:20.620+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:22:20.622+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:22:20.623+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:22:23.838+07:00  INFO 94209 --- [qtp1993997862-34] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:22:23.838+07:00  INFO 94209 --- [qtp1993997862-183] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:22:23.851+07:00  INFO 94209 --- [qtp1993997862-183] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:22:23.852+07:00  INFO 94209 --- [qtp1993997862-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:22:24.903+07:00 DEBUG 94209 --- [qtp1993997862-184] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:22:24.903+07:00 DEBUG 94209 --- [qtp1993997862-149] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:22:49.668+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:22:49.671+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:22:49.672+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:23:04.698+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:23:18.743+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-07-11T16:23:18.752+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:23:18.752+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:23:18.752+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:23:18.758+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T16:23:24.933+07:00 DEBUG 94209 --- [qtp1993997862-149] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:23:46.813+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:23:46.815+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:23:46.816+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:24:06.932+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:24:08.021+07:00  INFO 94209 --- [qtp1993997862-34] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:24:08.028+07:00  INFO 94209 --- [qtp1993997862-34] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:24:08.034+07:00  INFO 94209 --- [qtp1993997862-149] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:24:08.045+07:00  INFO 94209 --- [qtp1993997862-149] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:24:09.257+07:00 DEBUG 94209 --- [qtp1993997862-35] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:24:09.257+07:00 DEBUG 94209 --- [qtp1993997862-191] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:24:19.969+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:24:19.980+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:24:19.981+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:24:36.739+07:00  INFO 94209 --- [qtp1993997862-186] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:24:36.856+07:00  INFO 94209 --- [qtp1993997862-186] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:24:36.898+07:00  INFO 94209 --- [qtp1993997862-187] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:24:36.911+07:00  INFO 94209 --- [qtp1993997862-187] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:24:37.984+07:00 DEBUG 94209 --- [qtp1993997862-186] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:24:37.990+07:00 DEBUG 94209 --- [qtp1993997862-183] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:24:43.218+07:00  INFO 94209 --- [qtp1993997862-191] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:24:43.287+07:00  INFO 94209 --- [qtp1993997862-186] n.d.module.session.ClientSessionManager  : Add a client session id = node04ugwvk43uq1hmjvn8kum1f580, token = a74e0280d5130caa4eb4437006aa1067
2025-07-11T16:24:43.389+07:00  INFO 94209 --- [qtp1993997862-186] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:24:43.401+07:00  INFO 94209 --- [qtp1993997862-191] n.d.m.c.a.CompanyAuthenticationService   : User dan is logged in successfully system
2025-07-11T16:24:44.454+07:00 DEBUG 94209 --- [qtp1993997862-149] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:24:44.454+07:00 DEBUG 94209 --- [qtp1993997862-186] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:24:50.052+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:24:50.052+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:24:50.052+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:25:04.084+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:25:04.089+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T16:25:18.150+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T16:25:18.174+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:25:18.176+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:25:18.176+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:25:18.176+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T16:25:44.487+07:00 DEBUG 94209 --- [qtp1993997862-183] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:25:46.227+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:25:46.228+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:25:46.228+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:26:06.260+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:26:20.281+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:26:20.282+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:26:20.282+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:26:44.463+07:00 DEBUG 94209 --- [qtp1993997862-186] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = dan
2025-07-11T16:26:49.344+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:26:49.344+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:26:49.344+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:27:03.366+07:00  INFO 94209 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T16:27:17.401+07:00  INFO 94209 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-07-11T16:27:17.408+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T16:27:17.409+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node04ugwvk43uq1hmjvn8kum1f580, remote user dan
2025-07-11T16:27:17.409+07:00 DEBUG 94209 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T16:27:17.415+07:00  INFO 94209 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T16:27:41.958+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@34d9c9fe{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-11T16:27:41.964+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-11T16:27:41.964+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-11T16:27:41.964+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-11T16:27:41.967+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-11T16:27:41.967+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-11T16:27:41.967+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-11T16:27:41.967+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-11T16:27:41.967+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-11T16:27:41.967+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-11T16:27:41.967+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-11T16:27:41.967+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-11T16:27:41.967+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-11T16:27:41.967+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-11T16:27:41.967+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-11T16:27:41.967+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-11T16:27:42.000+07:00  INFO 94209 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T16:27:42.098+07:00  INFO 94209 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-11T16:27:42.103+07:00  INFO 94209 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-11T16:27:42.147+07:00  INFO 94209 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T16:27:42.169+07:00  INFO 94209 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T16:27:42.171+07:00  INFO 94209 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-11T16:27:42.173+07:00  INFO 94209 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-11T16:27:42.173+07:00  INFO 94209 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-11T16:27:42.314+07:00  INFO 94209 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-11T16:27:42.315+07:00  INFO 94209 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-11T16:27:42.315+07:00  INFO 94209 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-11T16:27:42.315+07:00  INFO 94209 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-11T16:27:42.316+07:00  INFO 94209 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-11T16:27:42.320+07:00  INFO 94209 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@6d985720{STOPPING}[12.0.15,sto=0]
2025-07-11T16:27:42.327+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-11T16:27:42.330+07:00  INFO 94209 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@50626d22{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5841096934721349836/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@734db023{STOPPED}}
