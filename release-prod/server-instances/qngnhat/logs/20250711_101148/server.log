2025-07-11T10:11:49.287+07:00  INFO 55243 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 55243 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-11T10:11:49.288+07:00  INFO 55243 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data"
2025-07-11T10:11:50.035+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.133+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 94 ms. Found 22 JPA repository interfaces.
2025-07-11T10:11:50.144+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.145+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-11T10:11:50.145+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.152+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-07-11T10:11:50.153+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.155+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-11T10:11:50.156+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.159+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T10:11:50.169+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.174+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-11T10:11:50.184+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.189+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-07-11T10:11:50.193+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.195+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-11T10:11:50.195+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.195+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T10:11:50.202+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.208+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-11T10:11:50.212+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.215+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-11T10:11:50.216+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.219+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T10:11:50.221+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.227+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-11T10:11:50.227+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.230+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-11T10:11:50.230+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.231+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T10:11:50.231+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.231+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-07-11T10:11:50.232+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.236+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-11T10:11:50.236+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.237+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-11T10:11:50.237+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.238+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T10:11:50.238+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.248+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-07-11T10:11:50.259+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.265+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-07-11T10:11:50.265+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.268+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-11T10:11:50.268+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.272+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-11T10:11:50.272+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.277+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-11T10:11:50.277+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.281+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T10:11:50.281+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.284+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-11T10:11:50.284+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.293+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-07-11T10:11:50.293+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.307+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 25 JPA repository interfaces.
2025-07-11T10:11:50.321+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.332+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-07-11T10:11:50.332+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.336+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T10:11:50.336+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.337+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-11T10:11:50.342+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.343+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T10:11:50.343+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.350+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-11T10:11:50.354+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.389+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 67 JPA repository interfaces.
2025-07-11T10:11:50.390+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.391+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-11T10:11:50.391+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T10:11:50.393+07:00  INFO 55243 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-11T10:11:50.608+07:00  INFO 55243 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-11T10:11:50.612+07:00  INFO 55243 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-11T10:11:50.882+07:00  WARN 55243 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-11T10:11:51.072+07:00  INFO 55243 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-11T10:11:51.074+07:00  INFO 55243 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-11T10:11:51.084+07:00  INFO 55243 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-11T10:11:51.084+07:00  INFO 55243 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1663 ms
2025-07-11T10:11:51.133+07:00  WARN 55243 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T10:11:51.133+07:00  INFO 55243 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-11T10:11:51.231+07:00  INFO 55243 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@1c9fa12
2025-07-11T10:11:51.232+07:00  INFO 55243 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-11T10:11:51.236+07:00  WARN 55243 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T10:11:51.236+07:00  INFO 55243 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-11T10:11:51.242+07:00  INFO 55243 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@474aeac5
2025-07-11T10:11:51.242+07:00  INFO 55243 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-11T10:11:51.242+07:00  WARN 55243 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T10:11:51.242+07:00  INFO 55243 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-11T10:11:51.726+07:00  INFO 55243 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6e1ef044
2025-07-11T10:11:51.726+07:00  INFO 55243 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-11T10:11:51.726+07:00  WARN 55243 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T10:11:51.726+07:00  INFO 55243 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-11T10:11:51.744+07:00  INFO 55243 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@5af7a7
2025-07-11T10:11:51.745+07:00  INFO 55243 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-11T10:11:51.745+07:00  INFO 55243 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-11T10:11:51.788+07:00  INFO 55243 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-11T10:11:51.790+07:00  INFO 55243 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@7cc742d{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5263159468297204761/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@22bf2a0d{STARTED}}
2025-07-11T10:11:51.790+07:00  INFO 55243 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@7cc742d{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5263159468297204761/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@22bf2a0d{STARTED}}
2025-07-11T10:11:51.792+07:00  INFO 55243 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@2810d9{STARTING}[12.0.15,sto=0] @3030ms
2025-07-11T10:11:51.842+07:00  INFO 55243 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11T10:11:51.870+07:00  INFO 55243 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-11T10:11:51.885+07:00  INFO 55243 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-11T10:11:52.008+07:00  INFO 55243 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-11T10:11:52.042+07:00  WARN 55243 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-11T10:11:52.669+07:00  INFO 55243 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-11T10:11:52.677+07:00  INFO 55243 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2b60c832] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-11T10:11:52.827+07:00  INFO 55243 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T10:11:53.008+07:00  INFO 55243 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "cloud.datatp.fforwarder.price", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-07-11T10:11:53.011+07:00  INFO 55243 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-11T10:11:53.019+07:00  INFO 55243 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11T10:11:53.020+07:00  INFO 55243 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-11T10:11:53.050+07:00  INFO 55243 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-11T10:11:53.060+07:00  WARN 55243 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-11T10:11:55.820+07:00  INFO 55243 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-11T10:11:55.821+07:00  INFO 55243 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6d872084] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-11T10:11:56.094+07:00  WARN 55243 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-07-11T10:11:56.095+07:00  WARN 55243 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-07-11T10:11:56.101+07:00  WARN 55243 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-07-11T10:11:56.101+07:00  WARN 55243 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-07-11T10:11:56.115+07:00  WARN 55243 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-07-11T10:11:56.115+07:00  WARN 55243 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-07-11T10:11:56.800+07:00  INFO 55243 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T10:11:56.847+07:00  INFO 55243 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-11T10:11:56.852+07:00  INFO 55243 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-11T10:11:56.852+07:00  INFO 55243 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-11T10:11:56.860+07:00  WARN 55243 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-11T10:11:57.000+07:00  INFO 55243 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-11T10:11:57.484+07:00  INFO 55243 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-11T10:11:57.487+07:00  INFO 55243 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-11T10:11:57.524+07:00  INFO 55243 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-11T10:11:57.567+07:00  INFO 55243 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-11T10:11:57.654+07:00  INFO 55243 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-11T10:11:57.688+07:00  INFO 55243 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-11T10:11:57.712+07:00  INFO 55243 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 7ms : this is harmless.
2025-07-11T10:11:57.721+07:00  INFO 55243 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-11T10:11:57.724+07:00  INFO 55243 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-11T10:11:57.739+07:00  INFO 55243 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 6096214ms : this is harmless.
2025-07-11T10:11:57.740+07:00  INFO 55243 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-11T10:11:57.753+07:00  INFO 55243 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-11T10:11:57.754+07:00  INFO 55243 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-11T10:12:00.341+07:00  INFO 55243 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 11/07/2025@10:00:00+0700 to 11/07/2025@10:15:00+0700
2025-07-11T10:12:00.341+07:00  INFO 55243 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 11/07/2025@10:00:00+0700 to 11/07/2025@10:15:00+0700
2025-07-11T10:12:00.954+07:00  INFO 55243 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-11T10:12:00.954+07:00  INFO 55243 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-11T10:12:00.954+07:00  WARN 55243 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-11T10:12:01.349+07:00  INFO 55243 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-11T10:12:01.349+07:00  INFO 55243 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-11T10:12:01.349+07:00  INFO 55243 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-11T10:12:01.349+07:00  INFO 55243 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-11T10:12:01.349+07:00  INFO 55243 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-11T10:12:02.829+07:00  WARN 55243 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 7199db2d-41bf-4364-acde-f2cd4fa2df51

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-11T10:12:02.832+07:00  INFO 55243 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-11T10:12:03.143+07:00  INFO 55243 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-11T10:12:03.143+07:00  INFO 55243 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-11T10:12:03.143+07:00  INFO 55243 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-11T10:12:03.143+07:00  INFO 55243 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-11T10:12:03.143+07:00  INFO 55243 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-11T10:12:03.143+07:00  INFO 55243 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-11T10:12:03.143+07:00  INFO 55243 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-11T10:12:03.143+07:00  INFO 55243 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-11T10:12:03.143+07:00  INFO 55243 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-11T10:12:03.143+07:00  INFO 55243 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-11T10:12:03.143+07:00  INFO 55243 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-11T10:12:03.143+07:00  INFO 55243 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-11T10:12:03.146+07:00  INFO 55243 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-11T10:12:03.146+07:00  INFO 55243 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-11T10:12:03.146+07:00  INFO 55243 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-11T10:12:03.224+07:00  INFO 55243 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11T10:12:03.225+07:00  INFO 55243 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-11T10:12:03.226+07:00  INFO 55243 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-11T10:12:03.234+07:00  INFO 55243 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@3d266efb{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-11T10:12:03.235+07:00  INFO 55243 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-11T10:12:03.236+07:00  INFO 55243 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-11T10:12:03.282+07:00  INFO 55243 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-11T10:12:03.282+07:00  INFO 55243 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-11T10:12:03.288+07:00  INFO 55243 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.303 seconds (process running for 14.526)
2025-07-11T10:12:22.791+07:00  INFO 55243 --- [qtp1002973859-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-11T10:12:28.976+07:00  INFO 55243 --- [qtp1002973859-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01qj8hvw7gsk5w1gj7ffww5z00g1
2025-07-11T10:12:28.976+07:00  INFO 55243 --- [qtp1002973859-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0lxzojrjmjbowy79nvkkdseqg0
2025-07-11T10:12:29.039+07:00 ERROR 55243 --- [qtp1002973859-35] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:153)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:138)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:104)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.produce(AdaptiveExecutionStrategy.java:195)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: net.datatp.util.error.RuntimeError: The authorized token is not set
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:124)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.validate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 97 common frames omitted
Caused by: net.datatp.util.error.RuntimeError: The authorized token is not set
	at net.datatp.util.ds.Objects.assertNotNull(Objects.java:28)
	at net.datatp.module.core.security.AuthorizationInfo.isAuthorized(AuthorizationInfo.java:33)
	at net.datatp.module.company.auth.CompanyAuthenticationService.doLogin(CompanyAuthenticationService.java:189)
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:122)
	... 108 common frames omitted

2025-07-11T10:12:29.042+07:00  INFO 55243 --- [qtp1002973859-35] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/validate
2025-07-11T10:12:29.039+07:00 ERROR 55243 --- [qtp1002973859-40] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:153)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:138)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:104)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: net.datatp.util.error.RuntimeError: The authorized token is not set
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:124)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.validate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted
Caused by: net.datatp.util.error.RuntimeError: The authorized token is not set
	at net.datatp.util.ds.Objects.assertNotNull(Objects.java:28)
	at net.datatp.module.core.security.AuthorizationInfo.isAuthorized(AuthorizationInfo.java:33)
	at net.datatp.module.company.auth.CompanyAuthenticationService.doLogin(CompanyAuthenticationService.java:189)
	at net.datatp.module.company.auth.CompanyAuthenticationService.validate(CompanyAuthenticationService.java:122)
	... 109 common frames omitted

2025-07-11T10:12:29.043+07:00  INFO 55243 --- [qtp1002973859-40] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/validate
2025-07-11T10:12:30.195+07:00  INFO 55243 --- [qtp1002973859-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01qj8hvw7gsk5w1gj7ffww5z00g1, token = 7480a5882fe2ff58353c1adf6faca356
2025-07-11T10:12:30.571+07:00  INFO 55243 --- [qtp1002973859-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T10:12:32.018+07:00 DEBUG 55243 --- [qtp1002973859-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:12:32.043+07:00 DEBUG 55243 --- [qtp1002973859-37] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:13:06.295+07:00  INFO 55243 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:13:06.307+07:00  INFO 55243 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:13:06.310+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:13:06.311+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:13:06.314+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:13:06.314+07:00  INFO 55243 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:13:06.418+07:00  INFO 55243 --- [botTaskExecutor-1] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6169] [company=beehph] type=ZALO for 11/07/2025@10:13:06+0700
2025-07-11T10:13:06.418+07:00  INFO 55243 --- [botTaskExecutor-1] c.d.f.s.message.MessageQueueManager      : Added message [6169] - scheduled at 11/07/2025@10:13:06+0700 - current session (11/07/2025@10:00:00+0700 to 11/07/2025@10:15:00+0700)

2025-07-11T10:13:32.050+07:00 DEBUG 55243 --- [qtp1002973859-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:13:39.363+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:13:39.365+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:13:39.365+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:14:02.406+07:00  INFO 55243 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:14:02.496+07:00  INFO 55243 --- [botTaskExecutor-2] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6170] [company=beehph] type=ZALO for 11/07/2025@10:14:02+0700
2025-07-11T10:14:02.497+07:00  INFO 55243 --- [botTaskExecutor-2] c.d.f.s.message.MessageQueueManager      : Added message [6170] - scheduled at 11/07/2025@10:14:02+0700 - current session (11/07/2025@10:00:00+0700 to 11/07/2025@10:15:00+0700)

2025-07-11T10:14:09.416+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:14:09.416+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:14:09.417+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:14:32.618+07:00 DEBUG 55243 --- [qtp1002973859-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:14:37.461+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:14:37.463+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:14:37.463+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:15:05.514+07:00  INFO 55243 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:15:05.519+07:00  INFO 55243 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-11T10:15:05.521+07:00  INFO 55243 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T10:15:05.522+07:00  INFO 55243 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 11/07/2025@10:15:05+0700
2025-07-11T10:15:05.546+07:00  INFO 55243 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 11/07/2025@10:15:00+0700 to 11/07/2025@10:30:00+0700
2025-07-11T10:15:05.547+07:00  INFO 55243 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 11/07/2025@10:15:00+0700 to 11/07/2025@10:30:00+0700
2025-07-11T10:15:05.570+07:00  INFO 55243 --- [botTaskExecutor-3] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6171] [company=beehph] type=ZALO for 11/07/2025@10:15:05+0700
2025-07-11T10:15:05.570+07:00  INFO 55243 --- [botTaskExecutor-3] c.d.f.s.message.MessageQueueManager      : Added message [6171] - scheduled at 11/07/2025@10:15:05+0700 - current session (11/07/2025@10:15:00+0700 to 11/07/2025@10:30:00+0700)

2025-07-11T10:15:05.580+07:00  INFO 55243 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-07-11T10:15:05.582+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:15:05.582+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:15:05.582+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:15:05.582+07:00  INFO 55243 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:15:11.628+07:00 DEBUG 55243 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "11/07/2025 00:00:00 GMT+0700",
      "toDate" : "11/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-11T10:15:32.060+07:00 DEBUG 55243 --- [qtp1002973859-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:15:39.665+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:15:39.667+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:15:39.668+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:16:06.713+07:00  INFO 55243 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:16:06.745+07:00  INFO 55243 --- [botTaskExecutor-1] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6172] [company=beehph] type=ZALO for 11/07/2025@10:16:06+0700
2025-07-11T10:16:06.746+07:00  INFO 55243 --- [botTaskExecutor-1] c.d.f.s.message.MessageQueueManager      : Added message [6172] - scheduled at 11/07/2025@10:16:06+0700 - current session (11/07/2025@10:15:00+0700 to 11/07/2025@10:30:00+0700)

2025-07-11T10:16:08.726+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:16:08.726+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:16:08.726+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:16:32.038+07:00 DEBUG 55243 --- [qtp1002973859-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:16:36.772+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:16:36.773+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:16:36.773+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:17:04.820+07:00  INFO 55243 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:17:04.882+07:00  INFO 55243 --- [botTaskExecutor-3] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6173] [company=beehph] type=ZALO for 11/07/2025@10:17:04+0700
2025-07-11T10:17:04.882+07:00  INFO 55243 --- [botTaskExecutor-3] c.d.f.s.message.MessageQueueManager      : Added message [6173] - scheduled at 11/07/2025@10:17:04+0700 - current session (11/07/2025@10:15:00+0700 to 11/07/2025@10:30:00+0700)

2025-07-11T10:17:09.865+07:00  INFO 55243 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T10:17:09.899+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:17:09.899+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:17:09.900+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:17:09.900+07:00  INFO 55243 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:17:12.092+07:00  INFO 55243 --- [qtp1002973859-63] n.d.module.session.ClientSessionManager  : Add a client session id = node01qj8hvw7gsk5w1gj7ffww5z00g1, token = 7480a5882fe2ff58353c1adf6faca356
2025-07-11T10:17:12.093+07:00  INFO 55243 --- [qtp1002973859-58] n.d.module.session.ClientSessionManager  : Add a client session id = node01qj8hvw7gsk5w1gj7ffww5z00g1, token = 7480a5882fe2ff58353c1adf6faca356
2025-07-11T10:17:12.106+07:00  INFO 55243 --- [qtp1002973859-63] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T10:17:12.108+07:00  INFO 55243 --- [qtp1002973859-58] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T10:17:13.434+07:00 DEBUG 55243 --- [qtp1002973859-64] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:17:13.434+07:00 DEBUG 55243 --- [qtp1002973859-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:17:39.961+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:17:39.964+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:17:39.966+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:18:07.010+07:00  INFO 55243 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:18:07.059+07:00  INFO 55243 --- [botTaskExecutor-2] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6174] [company=beehph] type=ZALO for 11/07/2025@10:18:07+0700
2025-07-11T10:18:07.060+07:00  INFO 55243 --- [botTaskExecutor-2] c.d.f.s.message.MessageQueueManager      : Added message [6174] - scheduled at 11/07/2025@10:18:07+0700 - current session (11/07/2025@10:15:00+0700 to 11/07/2025@10:30:00+0700)

2025-07-11T10:18:08.017+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:18:08.018+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:18:08.018+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:18:13.607+07:00 DEBUG 55243 --- [qtp1002973859-64] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:18:36.067+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:18:36.070+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:18:36.071+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:19:04.125+07:00  INFO 55243 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:19:04.186+07:00  INFO 55243 --- [botTaskExecutor-1] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6175] [company=beehph] type=ZALO for 11/07/2025@10:19:04+0700
2025-07-11T10:19:04.187+07:00  INFO 55243 --- [botTaskExecutor-1] c.d.f.s.message.MessageQueueManager      : Added message [6175] - scheduled at 11/07/2025@10:19:04+0700 - current session (11/07/2025@10:15:00+0700 to 11/07/2025@10:30:00+0700)

2025-07-11T10:19:10.156+07:00  INFO 55243 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-07-11T10:19:10.164+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:19:10.164+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:19:10.164+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:19:10.164+07:00  INFO 55243 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:19:13.600+07:00 DEBUG 55243 --- [qtp1002973859-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:19:39.204+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:19:39.204+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:19:39.205+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:20:06.258+07:00  INFO 55243 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:20:06.262+07:00  INFO 55243 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T10:20:06.303+07:00  INFO 55243 --- [botTaskExecutor-3] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6176] [company=beehph] type=ZALO for 11/07/2025@10:20:06+0700
2025-07-11T10:20:06.303+07:00  INFO 55243 --- [botTaskExecutor-3] c.d.f.s.message.MessageQueueManager      : Added message [6176] - scheduled at 11/07/2025@10:20:06+0700 - current session (11/07/2025@10:15:00+0700 to 11/07/2025@10:30:00+0700)

2025-07-11T10:20:07.268+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:20:07.268+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:20:07.268+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:20:13.597+07:00 DEBUG 55243 --- [qtp1002973859-64] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:20:35.322+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:20:35.323+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:20:35.323+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:21:03.374+07:00  INFO 55243 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:21:03.412+07:00  INFO 55243 --- [botTaskExecutor-2] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6177] [company=beehph] type=ZALO for 11/07/2025@10:21:03+0700
2025-07-11T10:21:03.412+07:00  INFO 55243 --- [botTaskExecutor-2] c.d.f.s.message.MessageQueueManager      : Added message [6177] - scheduled at 11/07/2025@10:21:03+0700 - current session (11/07/2025@10:15:00+0700 to 11/07/2025@10:30:00+0700)

2025-07-11T10:21:09.395+07:00  INFO 55243 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T10:21:09.398+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:21:09.398+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:21:09.398+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:21:09.399+07:00  INFO 55243 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:21:13.599+07:00 DEBUG 55243 --- [qtp1002973859-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:21:38.453+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:21:38.454+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:21:38.455+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:22:06.503+07:00  INFO 55243 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:22:06.504+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:22:06.504+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:22:06.504+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:22:06.544+07:00  INFO 55243 --- [botTaskExecutor-1] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6178] [company=beehph] type=ZALO for 11/07/2025@10:22:06+0700
2025-07-11T10:22:06.544+07:00  INFO 55243 --- [botTaskExecutor-1] c.d.f.s.message.MessageQueueManager      : Added message [6178] - scheduled at 11/07/2025@10:22:06+0700 - current session (11/07/2025@10:15:00+0700 to 11/07/2025@10:30:00+0700)

2025-07-11T10:22:13.595+07:00 DEBUG 55243 --- [qtp1002973859-64] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:22:39.557+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:22:39.559+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:22:39.559+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:22:51.816+07:00  INFO 55243 --- [Scheduler-*********-1] n.d.m.session.AppHttpSessionListener     : The session node0lxzojrjmjbowy79nvkkdseqg0 is destroyed.
2025-07-11T10:23:02.600+07:00  INFO 55243 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T10:23:02.637+07:00  INFO 55243 --- [botTaskExecutor-3] c.d.f.settings.message.CRMMessageLogic   : 📅 Scheduled message [id=6179] [company=beehph] type=ZALO for 11/07/2025@10:23:02+0700
2025-07-11T10:23:02.637+07:00  INFO 55243 --- [botTaskExecutor-3] c.d.f.s.message.MessageQueueManager      : Added message [6179] - scheduled at 11/07/2025@10:23:02+0700 - current session (11/07/2025@10:15:00+0700 to 11/07/2025@10:30:00+0700)

2025-07-11T10:23:09.625+07:00  INFO 55243 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-07-11T10:23:09.638+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:23:09.638+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:23:09.638+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:23:09.639+07:00  INFO 55243 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:23:22.621+07:00 DEBUG 55243 --- [qtp1002973859-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T10:23:37.687+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T10:23:37.688+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01qj8hvw7gsk5w1gj7ffww5z00g1, remote user nhat.le
2025-07-11T10:23:37.689+07:00 DEBUG 55243 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T10:23:49.107+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@3d266efb{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-11T10:23:49.108+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-11T10:23:49.108+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-11T10:23:49.108+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-11T10:23:49.109+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-11T10:23:49.109+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-11T10:23:49.109+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-11T10:23:49.109+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-11T10:23:49.109+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-11T10:23:49.109+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-11T10:23:49.109+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-11T10:23:49.109+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-11T10:23:49.109+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-11T10:23:49.109+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-11T10:23:49.109+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-11T10:23:49.109+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-11T10:23:49.127+07:00  INFO 55243 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T10:23:49.277+07:00  INFO 55243 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-11T10:23:49.281+07:00  INFO 55243 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-11T10:23:49.300+07:00  INFO 55243 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T10:23:49.302+07:00  INFO 55243 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T10:23:49.302+07:00  INFO 55243 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-11T10:23:49.303+07:00  INFO 55243 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-11T10:23:49.303+07:00  INFO 55243 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-11T10:23:49.439+07:00  INFO 55243 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-11T10:23:49.439+07:00  INFO 55243 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-11T10:23:49.440+07:00  INFO 55243 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-11T10:23:49.440+07:00  INFO 55243 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-11T10:23:49.440+07:00  INFO 55243 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-11T10:23:49.441+07:00  INFO 55243 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@2810d9{STOPPING}[12.0.15,sto=0]
2025-07-11T10:23:49.444+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-11T10:23:49.446+07:00  INFO 55243 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@7cc742d{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5263159468297204761/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@22bf2a0d{STOPPED}}
