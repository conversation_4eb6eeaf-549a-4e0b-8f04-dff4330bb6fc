2025-07-11T11:51:50.442+07:00  INFO 71373 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 71373 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-11T11:51:50.443+07:00  INFO 71373 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data"
2025-07-11T11:51:51.203+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.300+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 94 ms. Found 22 JPA repository interfaces.
2025-07-11T11:51:51.310+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.312+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-11T11:51:51.312+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.319+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 9 JPA repository interfaces.
2025-07-11T11:51:51.320+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.323+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-11T11:51:51.323+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.326+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T11:51:51.337+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.341+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-11T11:51:51.351+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.356+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-07-11T11:51:51.359+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.361+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-11T11:51:51.362+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.362+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T11:51:51.367+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.372+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-11T11:51:51.377+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.379+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-11T11:51:51.380+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.384+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-11T11:51:51.385+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.392+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-11T11:51:51.393+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.395+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-11T11:51:51.396+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.396+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T11:51:51.396+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.397+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-07-11T11:51:51.397+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.401+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-11T11:51:51.401+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.402+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-11T11:51:51.403+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.403+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T11:51:51.403+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.413+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-07-11T11:51:51.423+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.429+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-07-11T11:51:51.429+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.432+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-11T11:51:51.433+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.436+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-11T11:51:51.437+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.442+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-11T11:51:51.442+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.447+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-07-11T11:51:51.447+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.451+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-07-11T11:51:51.451+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.461+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 14 JPA repository interfaces.
2025-07-11T11:51:51.461+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.476+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 25 JPA repository interfaces.
2025-07-11T11:51:51.491+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.502+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 19 JPA repository interfaces.
2025-07-11T11:51:51.502+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.506+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T11:51:51.507+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.508+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-11T11:51:51.514+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.515+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T11:51:51.515+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.521+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-11T11:51:51.526+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.562+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 67 JPA repository interfaces.
2025-07-11T11:51:51.562+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.563+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-11T11:51:51.564+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T11:51:51.566+07:00  INFO 71373 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-11T11:51:51.747+07:00  INFO 71373 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-11T11:51:51.750+07:00  INFO 71373 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-11T11:51:52.024+07:00  WARN 71373 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-11T11:51:52.227+07:00  INFO 71373 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-11T11:51:52.229+07:00  INFO 71373 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-11T11:51:52.242+07:00  INFO 71373 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-11T11:51:52.242+07:00  INFO 71373 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1679 ms
2025-07-11T11:51:52.290+07:00  WARN 71373 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T11:51:52.290+07:00  INFO 71373 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-11T11:51:52.389+07:00  INFO 71373 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@2eb6a77f
2025-07-11T11:51:52.390+07:00  INFO 71373 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-11T11:51:52.395+07:00  WARN 71373 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T11:51:52.395+07:00  INFO 71373 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-11T11:51:52.402+07:00  INFO 71373 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@200ef05b
2025-07-11T11:51:52.402+07:00  INFO 71373 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-11T11:51:52.403+07:00  WARN 71373 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T11:51:52.403+07:00  INFO 71373 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-11T11:51:53.522+07:00  INFO 71373 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@46f43f50
2025-07-11T11:51:53.523+07:00  INFO 71373 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-11T11:51:53.523+07:00  WARN 71373 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T11:51:53.523+07:00  INFO 71373 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-11T11:51:53.529+07:00  INFO 71373 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@74691a1f
2025-07-11T11:51:53.529+07:00  INFO 71373 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-11T11:51:53.530+07:00  INFO 71373 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-11T11:51:53.579+07:00  INFO 71373 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-11T11:51:53.581+07:00  INFO 71373 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@10982760{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.1351139609445016823/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7cc742d{STARTED}}
2025-07-11T11:51:53.582+07:00  INFO 71373 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@10982760{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.1351139609445016823/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7cc742d{STARTED}}
2025-07-11T11:51:53.583+07:00  INFO 71373 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@4f94b2a8{STARTING}[12.0.15,sto=0] @3713ms
2025-07-11T11:51:53.632+07:00  INFO 71373 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11T11:51:53.659+07:00  INFO 71373 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-11T11:51:53.673+07:00  INFO 71373 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-11T11:51:53.797+07:00  INFO 71373 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-11T11:51:53.823+07:00  WARN 71373 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-11T11:51:54.391+07:00  INFO 71373 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-11T11:51:54.400+07:00  INFO 71373 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2b60c832] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-11T11:51:54.504+07:00  INFO 71373 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T11:51:54.724+07:00  INFO 71373 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "cloud.datatp.fforwarder.price", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-07-11T11:51:54.726+07:00  INFO 71373 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-11T11:51:54.735+07:00  INFO 71373 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11T11:51:54.736+07:00  INFO 71373 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-11T11:51:54.768+07:00  INFO 71373 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-11T11:51:54.772+07:00  WARN 71373 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-11T11:51:57.454+07:00  INFO 71373 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-11T11:51:57.455+07:00  INFO 71373 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@55587be4] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-11T11:51:57.706+07:00  WARN 71373 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-07-11T11:51:57.706+07:00  WARN 71373 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-07-11T11:51:57.713+07:00  WARN 71373 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-07-11T11:51:57.713+07:00  WARN 71373 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-07-11T11:51:57.729+07:00  WARN 71373 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-07-11T11:51:57.729+07:00  WARN 71373 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-07-11T11:51:58.353+07:00  INFO 71373 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T11:51:58.394+07:00  INFO 71373 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-11T11:51:58.399+07:00  INFO 71373 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-11T11:51:58.399+07:00  INFO 71373 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-11T11:51:58.406+07:00  WARN 71373 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-11T11:51:58.533+07:00  INFO 71373 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-11T11:51:58.991+07:00  INFO 71373 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-11T11:51:58.994+07:00  INFO 71373 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-11T11:51:59.029+07:00  INFO 71373 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-11T11:51:59.073+07:00  INFO 71373 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-11T11:51:59.245+07:00  INFO 71373 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-11T11:51:59.273+07:00  WARN 71373 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Attempting to allocate 100.0MB of offheap when there is only 71.2MB of free physical memory - some paging will therefore occur.
2025-07-11T11:51:59.273+07:00  INFO 71373 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-11T11:51:59.295+07:00  INFO 71373 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 6458528ms : this is harmless.
2025-07-11T11:51:59.303+07:00  INFO 71373 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-11T11:51:59.320+07:00  WARN 71373 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Attempting to allocate 100.0MB of offheap when there is only 80.8MB of free physical memory - some paging will therefore occur.
2025-07-11T11:51:59.320+07:00  INFO 71373 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-11T11:51:59.333+07:00  INFO 71373 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 12554736ms : this is harmless.
2025-07-11T11:51:59.335+07:00  INFO 71373 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-11T11:51:59.347+07:00  INFO 71373 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-11T11:51:59.348+07:00  INFO 71373 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-11T11:52:01.991+07:00  INFO 71373 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 11/07/2025@11:45:00+0700 to 11/07/2025@12:00:00+0700
2025-07-11T11:52:01.991+07:00  INFO 71373 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 11/07/2025@11:45:00+0700 to 11/07/2025@12:00:00+0700
2025-07-11T11:52:02.590+07:00  INFO 71373 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-11T11:52:02.590+07:00  INFO 71373 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-11T11:52:02.590+07:00  WARN 71373 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-11T11:52:02.996+07:00  INFO 71373 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-11T11:52:02.996+07:00  INFO 71373 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-11T11:52:02.996+07:00  INFO 71373 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-11T11:52:02.996+07:00  INFO 71373 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-11T11:52:02.996+07:00  INFO 71373 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-11T11:52:04.498+07:00  WARN 71373 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: c3e7250e-568e-4835-90ac-4c482a11d5ac

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-11T11:52:04.502+07:00  INFO 71373 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-11T11:52:04.828+07:00  INFO 71373 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-11T11:52:04.828+07:00  INFO 71373 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-11T11:52:04.828+07:00  INFO 71373 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-11T11:52:04.828+07:00  INFO 71373 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-11T11:52:04.828+07:00  INFO 71373 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-11T11:52:04.828+07:00  INFO 71373 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-11T11:52:04.828+07:00  INFO 71373 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-11T11:52:04.828+07:00  INFO 71373 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-11T11:52:04.828+07:00  INFO 71373 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-11T11:52:04.828+07:00  INFO 71373 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-11T11:52:04.828+07:00  INFO 71373 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-11T11:52:04.828+07:00  INFO 71373 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-11T11:52:04.831+07:00  INFO 71373 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-11T11:52:04.831+07:00  INFO 71373 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-11T11:52:04.831+07:00  INFO 71373 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-11T11:52:04.900+07:00  INFO 71373 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11T11:52:04.900+07:00  INFO 71373 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-11T11:52:04.902+07:00  INFO 71373 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-07-11T11:52:04.910+07:00  INFO 71373 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@28d4fcdf{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-11T11:52:04.911+07:00  INFO 71373 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-11T11:52:04.912+07:00  INFO 71373 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-11T11:52:04.969+07:00  INFO 71373 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-11T11:52:04.969+07:00  INFO 71373 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-11T11:52:04.975+07:00  INFO 71373 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.808 seconds (process running for 15.105)
2025-07-11T11:52:10.761+07:00  INFO 71373 --- [qtp1785356531-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01jii8vl88c6o74d08veehfqyx1
2025-07-11T11:52:10.761+07:00  INFO 71373 --- [qtp1785356531-34] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node080q156ah434syj3hg29nd8dr0
2025-07-11T11:52:10.924+07:00  INFO 71373 --- [qtp1785356531-34] n.d.module.session.ClientSessionManager  : Add a client session id = node080q156ah434syj3hg29nd8dr0, token = 43d819da9ef52f9de2b6b5bfe3e094bc
2025-07-11T11:52:10.936+07:00  INFO 71373 --- [qtp1785356531-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01jii8vl88c6o74d08veehfqyx1, token = 43d819da9ef52f9de2b6b5bfe3e094bc
2025-07-11T11:52:11.325+07:00  INFO 71373 --- [qtp1785356531-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T11:52:11.325+07:00  INFO 71373 --- [qtp1785356531-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T11:52:12.840+07:00 DEBUG 71373 --- [qtp1785356531-59] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T11:52:12.840+07:00 DEBUG 71373 --- [qtp1785356531-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T11:52:33.273+07:00  INFO 71373 --- [qtp1785356531-63] n.d.module.session.ClientSessionManager  : Add a client session id = node080q156ah434syj3hg29nd8dr0, token = 43d819da9ef52f9de2b6b5bfe3e094bc
2025-07-11T11:52:33.275+07:00  INFO 71373 --- [qtp1785356531-41] n.d.module.session.ClientSessionManager  : Add a client session id = node080q156ah434syj3hg29nd8dr0, token = 43d819da9ef52f9de2b6b5bfe3e094bc
2025-07-11T11:52:33.298+07:00  INFO 71373 --- [qtp1785356531-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T11:52:33.298+07:00  INFO 71373 --- [qtp1785356531-63] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T11:52:34.721+07:00 DEBUG 71373 --- [qtp1785356531-62] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T11:52:34.760+07:00 DEBUG 71373 --- [qtp1785356531-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T11:53:06.977+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T11:53:08.032+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-07-11T11:53:08.061+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T11:53:08.062+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T11:53:08.062+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T11:53:08.062+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T11:53:35.710+07:00 DEBUG 71373 --- [qtp1785356531-59] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T11:53:41.107+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T11:53:41.107+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T11:53:41.108+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T11:54:04.155+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T11:54:11.169+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T11:54:11.171+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T11:54:11.171+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T11:54:35.722+07:00 DEBUG 71373 --- [qtp1785356531-39] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T11:54:39.217+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T11:54:39.218+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T11:54:39.218+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T11:55:06.259+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T11:55:06.267+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T11:55:07.286+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T11:55:07.297+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T11:55:07.298+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T11:55:07.298+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T11:55:07.299+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T11:55:34.741+07:00 DEBUG 71373 --- [qtp1785356531-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T11:55:41.356+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T11:55:41.357+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T11:55:41.358+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T11:56:03.400+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T11:56:10.415+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T11:56:10.416+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T11:56:10.417+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T11:56:34.736+07:00 DEBUG 71373 --- [qtp1785356531-66] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T11:56:38.460+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T11:56:38.461+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T11:56:38.462+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T11:57:06.511+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T11:57:11.549+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-07-11T11:57:11.566+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T11:57:11.567+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T11:57:11.567+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T11:57:11.567+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T11:57:34.759+07:00 DEBUG 71373 --- [qtp1785356531-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T11:57:41.628+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T11:57:41.628+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T11:57:41.628+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T11:58:02.672+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T11:58:09.696+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T11:58:09.698+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T11:58:09.698+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T11:58:34.733+07:00 DEBUG 71373 --- [qtp1785356531-58] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T11:58:37.757+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T11:58:37.758+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T11:58:37.758+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T11:59:05.809+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T11:59:11.896+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-07-11T11:59:11.899+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T11:59:11.899+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T11:59:11.899+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T11:59:11.900+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T11:59:34.736+07:00 DEBUG 71373 --- [qtp1785356531-67] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T11:59:40.953+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T11:59:40.954+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T11:59:40.954+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:00:06.994+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:00:06.995+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T12:00:06.997+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-07-11T12:00:06.997+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 12 PM every day
2025-07-11T12:00:06.998+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-07-11T12:00:06.998+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-11T12:00:06.999+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 11/07/2025@12:00:06+0700
2025-07-11T12:00:07.429+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 11/07/2025@12:00:00+0700 to 11/07/2025@12:15:00+0700
2025-07-11T12:00:07.430+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 11/07/2025@12:00:00+0700 to 11/07/2025@12:15:00+0700
2025-07-11T12:00:08.462+07:00 DEBUG 71373 --- [botTaskExecutor-1] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "11/07/2025 00:00:00 GMT+0700",
      "toDate" : "11/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-11T12:00:09.449+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:00:09.449+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:00:09.449+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:00:34.745+07:00 DEBUG 71373 --- [qtp1785356531-64] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:00:37.495+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:00:37.495+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:00:37.496+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:01:05.538+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:01:11.566+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T12:01:11.574+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:01:11.575+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:01:11.575+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:01:11.576+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:01:34.746+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:01:40.627+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:01:40.627+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:01:40.627+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:02:06.672+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:02:08.681+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:02:08.681+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:02:08.682+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:02:34.759+07:00 DEBUG 71373 --- [qtp1785356531-66] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:02:41.736+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:02:41.737+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:02:41.737+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:02:53.622+07:00  INFO 71373 --- [Scheduler-161122793-1] n.d.m.session.AppHttpSessionListener     : The session node01jii8vl88c6o74d08veehfqyx1 is destroyed.
2025-07-11T12:03:04.782+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:03:11.802+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 2
2025-07-11T12:03:11.805+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:03:11.806+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:03:11.806+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:03:34.779+07:00  INFO 71373 --- [qtp1785356531-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-11T12:03:39.848+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:03:39.848+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:04:06.893+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:04:07.899+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:04:07.900+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:04:35.816+07:00  INFO 71373 --- [qtp1785356531-70] n.d.module.session.ClientSessionManager  : Add a client session id = node080q156ah434syj3hg29nd8dr0, token = 43d819da9ef52f9de2b6b5bfe3e094bc
2025-07-11T12:04:35.816+07:00  INFO 71373 --- [qtp1785356531-36] n.d.module.session.ClientSessionManager  : Add a client session id = node080q156ah434syj3hg29nd8dr0, token = 43d819da9ef52f9de2b6b5bfe3e094bc
2025-07-11T12:04:35.836+07:00  INFO 71373 --- [qtp1785356531-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T12:04:35.836+07:00  INFO 71373 --- [qtp1785356531-70] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T12:04:37.184+07:00 DEBUG 71373 --- [qtp1785356531-36] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:04:37.187+07:00 DEBUG 71373 --- [qtp1785356531-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:04:41.954+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:04:41.955+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:04:41.955+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:05:03.994+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:05:03.995+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T12:05:11.014+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-07-11T12:05:11.023+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:05:11.023+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:05:11.024+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:05:11.024+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:05:37.194+07:00 DEBUG 71373 --- [qtp1785356531-62] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:05:39.078+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:05:39.079+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:05:39.079+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:06:06.126+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:06:07.132+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:06:07.132+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:06:07.133+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:06:37.196+07:00 DEBUG 71373 --- [qtp1785356531-71] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:06:41.191+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:06:41.192+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:06:41.192+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:07:03.235+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:07:10.265+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 10
2025-07-11T12:07:10.269+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:07:10.270+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:07:10.270+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:07:10.270+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:07:37.198+07:00 DEBUG 71373 --- [qtp1785356531-62] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:07:38.349+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:07:38.349+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:07:38.349+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:08:06.424+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:08:11.432+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:08:11.432+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:08:11.432+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:08:37.194+07:00 DEBUG 71373 --- [qtp1785356531-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:08:41.526+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:08:41.527+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:08:41.527+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:09:02.597+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:09:09.612+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:09:09.613+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:09:09.614+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:09:09.614+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:09:09.614+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:09:37.197+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:09:37.705+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:09:37.705+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:09:37.705+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:10:05.798+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:10:05.798+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T12:10:11.811+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:10:11.812+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:10:11.812+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:10:37.195+07:00 DEBUG 71373 --- [qtp1785356531-71] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:10:40.893+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:10:40.894+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:10:40.894+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:11:06.972+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:11:08.988+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T12:11:08.992+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:11:08.993+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:11:08.993+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:11:08.993+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:11:37.072+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:11:37.073+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:11:37.073+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:11:37.212+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:12:05.125+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:12:11.141+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:12:11.141+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:12:11.141+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:12:37.202+07:00 DEBUG 71373 --- [qtp1785356531-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:12:40.236+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:12:40.236+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:12:40.236+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:13:06.315+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:13:08.336+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:13:08.337+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:13:08.338+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:13:08.338+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:13:08.338+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:13:37.199+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:13:41.428+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:13:41.429+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:13:41.429+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:14:04.503+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:14:11.528+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:14:11.529+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:14:11.529+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:14:37.199+07:00 DEBUG 71373 --- [qtp1785356531-71] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:14:39.599+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:14:39.599+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:14:39.600+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:15:06.662+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:15:06.663+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T12:15:06.663+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 11/07/2025@12:15:06+0700
2025-07-11T12:15:06.678+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 11/07/2025@12:15:00+0700 to 11/07/2025@12:30:00+0700
2025-07-11T12:15:06.679+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 11/07/2025@12:15:00+0700 to 11/07/2025@12:30:00+0700
2025-07-11T12:15:06.679+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-11T12:15:06.682+07:00 DEBUG 71373 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "11/07/2025 00:00:00 GMT+0700",
      "toDate" : "11/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-11T12:15:07.693+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-07-11T12:15:07.696+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:15:07.696+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:15:07.696+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:15:07.696+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:15:37.201+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:15:41.801+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:15:41.802+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:15:41.802+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:16:03.880+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:16:10.903+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:16:10.904+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:16:10.904+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:16:37.203+07:00 DEBUG 71373 --- [qtp1785356531-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:16:38.985+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:16:38.985+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:16:38.985+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:17:06.045+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:17:07.053+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T12:17:07.055+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:17:07.055+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:17:07.055+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:17:07.056+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:17:37.206+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:17:41.153+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:17:41.153+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:17:41.154+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:18:03.220+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:18:10.236+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:18:10.237+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:18:10.237+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:18:37.203+07:00 DEBUG 71373 --- [qtp1785356531-71] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:18:38.301+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:18:38.301+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:18:38.301+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:19:06.379+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:19:11.387+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T12:19:11.392+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:19:11.392+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:19:11.392+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:19:11.392+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:19:37.196+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:19:41.462+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:19:41.463+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:19:41.463+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:20:02.528+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:20:02.529+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T12:20:09.553+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:20:09.553+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:20:09.553+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:20:37.213+07:00 DEBUG 71373 --- [qtp1785356531-89] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:20:37.637+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:20:37.638+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:20:37.638+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:21:05.715+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:21:11.736+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T12:21:11.740+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:21:11.740+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:21:11.741+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:21:11.741+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:21:37.204+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:21:40.841+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:21:40.842+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:21:40.842+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:22:06.927+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:22:08.947+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:22:08.948+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:22:08.948+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:22:37.789+07:00 DEBUG 71373 --- [qtp1785356531-71] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:22:42.015+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:22:42.016+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:22:42.017+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:23:05.102+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:23:11.115+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:23:11.116+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:23:11.116+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:23:11.116+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:23:11.117+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:23:40.200+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:23:40.201+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:23:40.202+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:24:06.261+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:24:08.277+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:24:08.278+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:24:08.278+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:24:22.865+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:24:41.365+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:24:41.365+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:24:41.365+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:25:04.439+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:25:04.440+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T12:25:11.482+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T12:25:11.487+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:25:11.487+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:25:11.488+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:25:11.488+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:25:22.828+07:00 DEBUG 71373 --- [qtp1785356531-71] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:25:39.547+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:25:39.548+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:25:39.548+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:26:06.613+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:26:07.621+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:26:07.621+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:26:07.622+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:26:22.810+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:26:41.715+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:26:41.716+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:26:41.716+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:27:03.794+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:27:10.814+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:27:10.815+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:27:10.816+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:27:10.816+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:27:10.816+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:27:22.800+07:00 DEBUG 71373 --- [qtp1785356531-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:27:38.908+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:27:38.909+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:27:38.910+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:28:07.003+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:28:12.011+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:28:12.011+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:28:12.012+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:28:22.811+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:28:41.122+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:28:41.122+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:28:41.122+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:29:03.179+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:29:10.206+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T12:29:10.209+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:29:10.209+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:29:10.210+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:29:10.210+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:29:22.813+07:00 DEBUG 71373 --- [qtp1785356531-71] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:29:38.281+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:29:38.285+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:29:38.285+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:30:06.351+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:30:06.352+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T12:30:06.352+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-11T12:30:06.352+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 11/07/2025@12:30:06+0700
2025-07-11T12:30:06.362+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 11/07/2025@12:30:00+0700 to 11/07/2025@12:45:00+0700
2025-07-11T12:30:06.363+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 11/07/2025@12:30:00+0700 to 11/07/2025@12:45:00+0700
2025-07-11T12:30:11.368+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:30:11.371+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:30:11.372+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:30:12.377+07:00 DEBUG 71373 --- [botTaskExecutor-1] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "11/07/2025 00:00:00 GMT+0700",
      "toDate" : "11/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-11T12:30:22.787+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:30:41.461+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:30:41.462+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:30:41.462+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:31:02.519+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:31:09.552+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T12:31:09.557+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:31:09.558+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:31:09.558+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:31:09.558+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:31:22.796+07:00 DEBUG 71373 --- [qtp1785356531-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:31:37.623+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:31:37.624+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:31:37.625+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:32:05.691+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:32:11.702+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:32:11.703+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:32:11.703+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:32:22.768+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:32:40.797+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:32:40.798+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:32:40.798+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:33:06.863+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:33:08.884+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T12:33:08.887+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:33:08.888+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:33:08.888+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:33:08.888+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:33:22.780+07:00 DEBUG 71373 --- [qtp1785356531-71] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:33:41.976+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:33:41.978+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:33:41.978+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:34:05.054+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:34:11.066+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:34:11.066+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:34:11.066+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:34:22.771+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:34:40.137+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:34:40.141+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:34:40.141+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:35:06.207+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T12:35:06.208+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:35:08.217+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:35:08.219+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:35:08.219+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:35:08.219+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:35:08.219+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:35:22.759+07:00 DEBUG 71373 --- [qtp1785356531-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:35:41.324+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:35:41.325+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:35:41.325+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:36:04.406+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:36:11.424+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:36:11.424+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:36:11.424+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:36:22.766+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:36:39.505+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:36:39.506+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:36:39.507+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:37:06.586+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:37:07.600+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T12:37:07.604+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:37:07.604+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:37:07.604+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:37:07.605+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:37:22.785+07:00 DEBUG 71373 --- [qtp1785356531-71] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:37:41.676+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:37:41.677+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:37:41.678+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:38:03.745+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:38:10.764+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:38:10.765+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:38:10.765+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:38:22.775+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:38:38.828+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:38:38.829+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:38:38.829+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:39:06.918+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:39:11.927+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T12:39:11.930+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:39:11.931+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:39:11.931+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:39:11.931+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:39:22.758+07:00 DEBUG 71373 --- [qtp1785356531-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:39:41.036+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:39:41.039+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:39:41.039+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:40:03.107+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:40:03.109+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T12:40:10.124+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:40:10.124+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:40:10.124+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:40:22.779+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:40:38.215+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:40:38.216+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:40:38.216+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:41:06.294+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:41:11.305+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-07-11T12:41:11.307+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:41:11.307+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:41:11.307+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:41:11.308+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:41:22.773+07:00 DEBUG 71373 --- [qtp1785356531-71] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:41:41.388+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:41:41.389+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:41:41.389+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:42:02.449+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:42:09.467+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:42:09.468+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:42:09.468+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:42:22.794+07:00 DEBUG 71373 --- [qtp1785356531-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:42:37.564+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:42:37.566+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:42:37.566+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:43:05.648+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:43:11.658+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T12:43:11.660+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:43:11.660+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:43:11.660+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:43:11.660+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:43:22.810+07:00 DEBUG 71373 --- [qtp1785356531-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:43:40.760+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:43:40.761+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:43:40.761+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:44:06.831+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:44:08.850+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:44:08.850+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:44:08.850+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:44:22.800+07:00 DEBUG 71373 --- [qtp1785356531-89] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:44:41.940+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:44:41.942+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:44:41.942+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:45:05.011+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-11T12:45:05.012+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 11/07/2025@12:45:05+0700
2025-07-11T12:45:05.020+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 11/07/2025@12:45:00+0700 to 11/07/2025@13:00:00+0700
2025-07-11T12:45:05.020+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 11/07/2025@12:45:00+0700 to 11/07/2025@13:00:00+0700
2025-07-11T12:45:05.021+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:45:05.021+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T12:45:11.036+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:45:11.038+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:45:11.038+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:45:11.038+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:45:11.039+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:45:11.041+07:00 DEBUG 71373 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "11/07/2025 00:00:00 GMT+0700",
      "toDate" : "11/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-11T12:45:22.765+07:00 DEBUG 71373 --- [qtp1785356531-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:45:40.128+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:45:40.129+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:45:40.129+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:46:06.189+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:46:08.201+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:46:08.201+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:46:08.201+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:46:22.750+07:00 DEBUG 71373 --- [qtp1785356531-89] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:46:41.288+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:46:41.289+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:46:41.290+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:47:04.360+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:47:11.386+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-11T12:47:11.389+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:47:11.390+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:47:11.390+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:47:11.390+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:47:22.777+07:00 DEBUG 71373 --- [qtp1785356531-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:47:39.470+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:47:39.471+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:47:39.471+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:48:06.563+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:48:07.573+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:48:07.574+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:48:07.574+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:48:22.806+07:00 DEBUG 71373 --- [qtp1785356531-89] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:48:41.670+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:48:41.671+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:48:41.671+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:49:03.723+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:49:10.738+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:49:10.740+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:49:10.741+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:49:10.741+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:49:10.741+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:49:22.803+07:00 DEBUG 71373 --- [qtp1785356531-71] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:49:38.824+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:49:38.825+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:49:38.825+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:50:06.895+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:50:06.896+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T12:50:11.902+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:50:11.903+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:50:11.903+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:50:22.800+07:00 DEBUG 71373 --- [qtp1785356531-89] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:50:42.011+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:50:42.012+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:50:42.012+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:51:03.090+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:51:10.125+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T12:51:10.129+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:51:10.129+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:51:10.129+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:51:10.129+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:51:22.797+07:00 DEBUG 71373 --- [qtp1785356531-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:51:38.207+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:51:38.208+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:51:38.208+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:52:06.301+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:52:11.312+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:52:11.312+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:52:11.312+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:52:22.858+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:52:41.406+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:52:41.406+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:52:41.407+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:53:02.450+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:53:09.467+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:53:09.469+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:53:09.469+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:53:09.469+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:53:09.469+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:53:22.802+07:00 DEBUG 71373 --- [qtp1785356531-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:53:37.558+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:53:37.559+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:53:37.559+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:54:05.642+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:54:11.649+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:54:11.649+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:54:11.650+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:54:22.800+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:54:40.739+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:54:40.739+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:54:40.739+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:55:06.812+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:55:06.813+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T12:55:08.839+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T12:55:08.843+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:55:08.844+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:55:08.844+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:55:08.844+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:55:22.795+07:00 DEBUG 71373 --- [qtp1785356531-89] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:55:41.936+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:55:41.937+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:55:41.938+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:56:04.996+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:56:12.006+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:56:12.006+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:56:12.006+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:56:22.796+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:56:40.064+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:56:40.067+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:56:40.067+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:57:06.120+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:57:08.126+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-07-11T12:57:08.128+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:57:08.128+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:57:08.128+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:57:08.129+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:57:22.795+07:00 DEBUG 71373 --- [qtp1785356531-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:57:41.235+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:57:41.236+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:57:41.236+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:58:04.324+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:58:11.343+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:58:11.344+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:58:11.344+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:58:22.820+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:58:39.443+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:58:39.444+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:58:39.444+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:59:06.504+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T12:59:07.521+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T12:59:07.525+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:59:07.525+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:59:07.526+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T12:59:07.526+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T12:59:22.809+07:00 DEBUG 71373 --- [qtp1785356531-89] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T12:59:41.621+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T12:59:41.622+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T12:59:41.623+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:00:03.677+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:00:03.679+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T13:00:03.679+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-11T13:00:03.680+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 11/07/2025@13:00:03+0700
2025-07-11T13:00:03.693+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 11/07/2025@13:00:00+0700 to 11/07/2025@13:15:00+0700
2025-07-11T13:00:03.694+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 11/07/2025@13:00:00+0700 to 11/07/2025@13:15:00+0700
2025-07-11T13:00:03.694+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-07-11T13:00:09.706+07:00 DEBUG 71373 --- [botTaskExecutor-3] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "11/07/2025 00:00:00 GMT+0700",
      "toDate" : "11/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-11T13:00:10.709+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:00:10.710+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:00:10.710+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:00:22.784+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:00:38.789+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:00:38.790+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:00:38.791+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:01:06.870+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:01:11.892+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-11T13:01:11.901+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:01:11.901+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:01:11.901+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:01:11.901+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:01:22.793+07:00 DEBUG 71373 --- [qtp1785356531-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:01:41.997+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:01:41.997+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:01:41.998+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:02:03.058+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:02:10.082+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:02:10.082+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:02:10.082+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:02:22.798+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:02:38.141+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:02:38.143+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:02:38.143+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:03:06.224+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:03:11.234+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:03:11.235+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:03:11.235+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:03:11.235+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:03:11.235+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:03:22.813+07:00 DEBUG 71373 --- [qtp1785356531-89] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:03:41.326+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:03:41.327+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:03:41.327+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:04:02.401+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:04:09.426+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:04:09.426+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:04:09.427+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:04:22.791+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:04:37.513+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:04:37.513+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:04:37.513+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:05:05.593+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:05:05.593+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T13:05:11.620+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T13:05:11.625+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:05:11.625+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:05:11.625+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:05:11.625+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:05:22.809+07:00 DEBUG 71373 --- [qtp1785356531-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:05:40.717+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:05:40.718+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:05:40.718+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:06:06.791+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:06:08.805+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:06:08.805+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:06:08.805+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:06:22.854+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:06:41.861+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:06:41.861+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:06:41.861+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:07:04.939+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:07:11.971+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T13:07:11.977+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:07:11.977+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:07:11.977+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:07:11.977+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:07:22.798+07:00 DEBUG 71373 --- [qtp1785356531-89] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:07:40.067+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:07:40.068+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:07:40.068+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:08:06.134+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:08:08.145+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:08:08.145+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:08:08.145+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:08:22.802+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:08:41.231+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:08:41.231+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:08:41.232+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:09:04.301+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:09:11.314+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:09:11.316+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:09:11.316+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:09:11.316+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:09:11.317+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:09:22.820+07:00 DEBUG 71373 --- [qtp1785356531-61] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:09:39.390+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:09:39.392+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:09:39.392+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:10:06.464+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:10:06.466+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T13:10:07.476+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:10:07.477+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:10:07.477+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:10:22.810+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:10:41.582+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:10:41.583+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:10:41.583+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:11:03.634+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:11:10.662+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T13:11:10.665+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:11:10.665+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:11:10.665+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:11:10.665+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:11:22.864+07:00 DEBUG 71373 --- [qtp1785356531-182] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:11:38.734+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:11:38.735+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:11:38.735+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:12:06.816+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:12:11.825+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:12:11.825+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:12:11.826+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:12:22.819+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:12:41.908+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:12:41.910+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:12:41.910+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:13:02.954+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:13:09.981+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:13:09.983+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:13:09.983+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:13:09.983+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:13:09.984+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:13:22.809+07:00 DEBUG 71373 --- [qtp1785356531-188] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:13:38.066+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:13:38.066+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:13:38.066+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:14:06.142+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:14:11.152+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:14:11.153+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:14:11.153+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:14:22.775+07:00 DEBUG 71373 --- [qtp1785356531-89] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:14:41.248+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:14:41.248+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:14:41.248+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:15:02.310+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T13:15:02.311+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-11T13:15:02.312+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 11/07/2025@13:15:02+0700
2025-07-11T13:15:02.321+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 11/07/2025@13:15:00+0700 to 11/07/2025@13:30:00+0700
2025-07-11T13:15:02.322+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 11/07/2025@13:15:00+0700 to 11/07/2025@13:30:00+0700
2025-07-11T13:15:02.322+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:15:08.339+07:00 DEBUG 71373 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "11/07/2025 00:00:00 GMT+0700",
      "toDate" : "11/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-11T13:15:09.348+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T13:15:09.350+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:15:09.350+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:15:09.350+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:15:09.350+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:15:22.782+07:00 DEBUG 71373 --- [qtp1785356531-188] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:15:37.422+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:15:37.422+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:15:37.423+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:16:05.488+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:16:11.508+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:16:11.509+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:16:11.509+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:16:22.771+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:16:40.593+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:16:40.594+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:16:40.594+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:17:06.662+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:17:08.678+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T13:17:08.682+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:17:08.682+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:17:08.682+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:17:08.682+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:17:22.833+07:00 DEBUG 71373 --- [qtp1785356531-183] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:17:41.784+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:17:41.785+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:17:41.785+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:18:04.856+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:18:11.879+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:18:11.880+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:18:11.880+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:18:22.769+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:18:39.973+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:18:39.974+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:18:39.975+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:19:06.061+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:19:08.087+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T13:19:08.091+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:19:08.091+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:19:08.091+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:19:08.092+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:19:22.780+07:00 DEBUG 71373 --- [qtp1785356531-188] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:19:41.186+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:19:41.188+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:19:41.188+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:20:04.266+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:20:04.267+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T13:20:11.283+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:20:11.283+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:20:11.283+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:20:22.769+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:20:39.360+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:20:39.362+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:20:39.362+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:21:06.435+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:21:07.442+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:21:07.443+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:21:07.443+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:21:07.443+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:21:07.444+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:21:22.775+07:00 DEBUG 71373 --- [qtp1785356531-183] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:21:41.536+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:21:41.536+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:21:41.536+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:22:03.600+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:22:10.619+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:22:10.620+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:22:10.620+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:22:22.781+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:22:38.689+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:22:38.689+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:22:38.689+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:23:06.762+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:23:11.777+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T13:23:11.782+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:23:11.782+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:23:11.782+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:23:11.782+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:23:22.783+07:00 DEBUG 71373 --- [qtp1785356531-188] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:23:41.863+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:23:41.863+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:23:41.863+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:24:02.930+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:24:09.948+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:24:09.949+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:24:09.949+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:24:22.762+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:24:38.029+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:24:38.030+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:24:38.030+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:25:06.119+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:25:06.120+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T13:25:11.131+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-07-11T13:25:11.132+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:25:11.133+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:25:11.133+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:25:11.133+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:25:22.775+07:00 DEBUG 71373 --- [qtp1785356531-183] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:25:41.242+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:25:41.243+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:25:41.243+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:26:02.294+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:26:09.317+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:26:09.318+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:26:09.318+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:26:22.823+07:00 DEBUG 71373 --- [qtp1785356531-182] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:26:37.400+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:26:37.401+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:26:37.401+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:27:05.480+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:27:11.498+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T13:27:11.504+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:27:11.504+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:27:11.504+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:27:11.505+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:27:22.799+07:00 DEBUG 71373 --- [qtp1785356531-188] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:27:40.593+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:27:40.595+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:27:40.595+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:28:06.666+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:28:08.675+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:28:08.676+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:28:08.676+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:28:22.767+07:00 DEBUG 71373 --- [qtp1785356531-182] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:28:41.736+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:28:41.738+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:28:41.738+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:29:04.820+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:29:11.843+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T13:29:11.847+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:29:11.847+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:29:11.847+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:29:11.848+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:29:22.776+07:00 DEBUG 71373 --- [qtp1785356531-183] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:29:39.934+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:29:39.934+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:29:39.934+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:30:05.990+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T13:30:05.991+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-11T13:30:05.992+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 11/07/2025@13:30:05+0700
2025-07-11T13:30:06.001+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 11/07/2025@13:30:00+0700 to 11/07/2025@13:45:00+0700
2025-07-11T13:30:06.001+07:00  INFO 71373 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 11/07/2025@13:30:00+0700 to 11/07/2025@13:45:00+0700
2025-07-11T13:30:07.005+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:30:07.007+07:00 DEBUG 71373 --- [botTaskExecutor-1] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "11/07/2025 00:00:00 GMT+0700",
      "toDate" : "11/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-11T13:30:08.009+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:30:08.009+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:30:08.009+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:30:22.757+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:30:41.081+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:30:41.082+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:30:41.083+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:31:04.154+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:31:11.188+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T13:31:11.192+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:31:11.192+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:31:11.192+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:31:11.192+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:31:22.756+07:00 DEBUG 71373 --- [qtp1785356531-188] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:31:39.281+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:31:39.286+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:31:39.286+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:32:06.367+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:32:07.371+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:32:07.372+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:32:07.372+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:32:22.770+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:32:41.454+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:32:41.455+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:32:41.455+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:33:03.535+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:33:10.569+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T13:33:10.573+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:33:10.573+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:33:10.573+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:33:10.574+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:33:22.766+07:00 DEBUG 71373 --- [qtp1785356531-183] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:33:38.657+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:33:38.658+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:33:38.658+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:34:06.743+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:34:11.754+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:34:11.754+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:34:11.754+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:34:22.741+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:34:41.846+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:34:41.848+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:34:41.848+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:35:02.919+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T13:35:02.921+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:35:09.950+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:35:09.951+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:35:09.952+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:35:09.952+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:35:09.952+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:35:22.782+07:00 DEBUG 71373 --- [qtp1785356531-251] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:35:38.025+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:35:38.026+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:35:38.026+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:36:06.094+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:36:11.128+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:36:11.134+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:36:11.141+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:36:11.489+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:36:37.146+07:00 DEBUG 71373 --- [qtp1785356531-183] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:36:41.446+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:36:41.446+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:36:41.446+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:37:02.478+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:37:09.500+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T13:37:09.504+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:37:09.504+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:37:09.504+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:37:09.504+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:37:37.147+07:00 DEBUG 71373 --- [qtp1785356531-158] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:37:37.557+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:37:37.557+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:37:37.557+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:38:05.597+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:38:11.604+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:38:11.605+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:38:11.605+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:38:37.179+07:00 DEBUG 71373 --- [qtp1785356531-183] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T13:38:40.656+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:38:40.656+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:38:40.656+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:39:06.692+07:00  INFO 71373 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T13:39:08.706+07:00  INFO 71373 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:39:08.708+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T13:39:08.709+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node080q156ah434syj3hg29nd8dr0, remote user nhat.le
2025-07-11T13:39:08.709+07:00 DEBUG 71373 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T13:39:08.710+07:00  INFO 71373 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:39:19.445+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@28d4fcdf{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-11T13:39:19.447+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-11T13:39:19.447+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-11T13:39:19.447+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-11T13:39:19.447+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-11T13:39:19.447+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-11T13:39:19.447+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-11T13:39:19.447+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-11T13:39:19.447+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-11T13:39:19.447+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-11T13:39:19.447+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-11T13:39:19.447+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-11T13:39:19.447+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-11T13:39:19.447+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-11T13:39:19.447+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-11T13:39:19.447+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-11T13:39:19.461+07:00  INFO 71373 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T13:39:19.543+07:00  INFO 71373 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-11T13:39:19.549+07:00  INFO 71373 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-11T13:39:19.569+07:00  INFO 71373 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T13:39:19.571+07:00  INFO 71373 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T13:39:19.572+07:00  INFO 71373 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-11T13:39:19.572+07:00  INFO 71373 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-11T13:39:19.572+07:00  INFO 71373 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-11T13:39:19.711+07:00  INFO 71373 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-11T13:39:19.711+07:00  INFO 71373 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-11T13:39:19.712+07:00  INFO 71373 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-11T13:39:19.712+07:00  INFO 71373 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-11T13:39:19.712+07:00  INFO 71373 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-11T13:39:19.714+07:00  INFO 71373 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@4f94b2a8{STOPPING}[12.0.15,sto=0]
2025-07-11T13:39:19.719+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-11T13:39:19.720+07:00  INFO 71373 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@10982760{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.1351139609445016823/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7cc742d{STOPPED}}
