2025-07-11T14:11:17.424+07:00  INFO 88063 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 88063 (/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-prod/server)
2025-07-11T14:11:17.424+07:00  INFO 88063 --- [main] net.datatp.server.ServerApp              : The following 8 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data"
2025-07-11T14:11:18.169+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.273+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 101 ms. Found 22 JPA repository interfaces.
2025-07-11T14:11:18.283+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.284+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-11T14:11:18.285+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.291+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-07-11T14:11:18.292+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.294+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-11T14:11:18.294+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.298+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T14:11:18.309+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.314+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-07-11T14:11:18.324+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.328+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-07-11T14:11:18.333+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.335+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-11T14:11:18.336+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.336+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T14:11:18.341+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.347+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-07-11T14:11:18.352+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.355+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-07-11T14:11:18.355+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.359+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T14:11:18.360+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.368+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-07-11T14:11:18.368+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.371+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-11T14:11:18.371+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.371+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T14:11:18.372+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.372+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-07-11T14:11:18.373+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.377+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-07-11T14:11:18.377+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.378+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-11T14:11:18.378+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.379+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T14:11:18.379+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.389+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-07-11T14:11:18.400+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.406+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-07-11T14:11:18.406+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.408+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-11T14:11:18.409+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.412+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-07-11T14:11:18.413+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.417+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-07-11T14:11:18.418+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.421+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T14:11:18.421+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.424+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-07-11T14:11:18.424+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.433+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-07-11T14:11:18.434+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.447+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 25 JPA repository interfaces.
2025-07-11T14:11:18.461+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.472+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 19 JPA repository interfaces.
2025-07-11T14:11:18.473+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.477+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-07-11T14:11:18.477+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.478+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-07-11T14:11:18.483+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.484+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-07-11T14:11:18.484+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.490+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-07-11T14:11:18.494+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.531+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 67 JPA repository interfaces.
2025-07-11T14:11:18.532+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.533+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-07-11T14:11:18.533+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11T14:11:18.536+07:00  INFO 88063 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-07-11T14:11:18.783+07:00  INFO 88063 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-11T14:11:18.787+07:00  INFO 88063 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-11T14:11:19.064+07:00  WARN 88063 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-07-11T14:11:19.290+07:00  INFO 88063 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-07-11T14:11:19.292+07:00  INFO 88063 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-07-11T14:11:19.304+07:00  INFO 88063 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-07-11T14:11:19.304+07:00  INFO 88063 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1772 ms
2025-07-11T14:11:19.354+07:00  WARN 88063 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T14:11:19.354+07:00  INFO 88063 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-07-11T14:11:19.446+07:00  INFO 88063 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@549fc0b3
2025-07-11T14:11:19.447+07:00  INFO 88063 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-07-11T14:11:19.452+07:00  WARN 88063 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T14:11:19.452+07:00  INFO 88063 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-07-11T14:11:19.457+07:00  INFO 88063 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@7cb9295f
2025-07-11T14:11:19.457+07:00  INFO 88063 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-07-11T14:11:19.457+07:00  WARN 88063 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T14:11:19.457+07:00  INFO 88063 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-07-11T14:11:19.944+07:00  INFO 88063 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3c92f2f9
2025-07-11T14:11:19.944+07:00  INFO 88063 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-07-11T14:11:19.944+07:00  WARN 88063 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-07-11T14:11:19.944+07:00  INFO 88063 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-07-11T14:11:19.952+07:00  INFO 88063 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@685e8e17
2025-07-11T14:11:19.952+07:00  INFO 88063 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-07-11T14:11:19.952+07:00  INFO 88063 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************'
2025-07-11T14:11:20.010+07:00  INFO 88063 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-07-11T14:11:20.013+07:00  INFO 88063 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@734db023{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16715675871874313929/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@61774805{STARTED}}
2025-07-11T14:11:20.013+07:00  INFO 88063 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@734db023{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16715675871874313929/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@61774805{STARTED}}
2025-07-11T14:11:20.015+07:00  INFO 88063 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@408f3054{STARTING}[12.0.15,sto=0] @3219ms
2025-07-11T14:11:20.109+07:00  INFO 88063 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11T14:11:20.147+07:00  INFO 88063 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-11T14:11:20.163+07:00  INFO 88063 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-11T14:11:20.297+07:00  INFO 88063 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-11T14:11:20.330+07:00  WARN 88063 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-11T14:11:20.975+07:00  INFO 88063 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-11T14:11:20.985+07:00  INFO 88063 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@52463130] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-11T14:11:21.106+07:00  INFO 88063 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T14:11:21.265+07:00  INFO 88063 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages: 
 [ "net.datatp.module.project", "cloud.datatp.fforwarder.mgmt", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "cloud.datatp.fforwarder.price", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.tms", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "cloud.datatp.fforwarder.sales", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-07-11T14:11:21.267+07:00  INFO 88063 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-07-11T14:11:21.275+07:00  INFO 88063 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11T14:11:21.277+07:00  INFO 88063 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-07-11T14:11:21.309+07:00  INFO 88063 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-11T14:11:21.313+07:00  WARN 88063 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-11T14:11:24.335+07:00  INFO 88063 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-11T14:11:24.336+07:00  INFO 88063 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@10af83ad] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-07-11T14:11:24.845+07:00  WARN 88063 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-07-11T14:11:24.845+07:00  WARN 88063 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-07-11T14:11:24.852+07:00  WARN 88063 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-07-11T14:11:24.852+07:00  WARN 88063 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-07-11T14:11:24.866+07:00  WARN 88063 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-07-11T14:11:24.866+07:00  WARN 88063 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-07-11T14:11:25.574+07:00  INFO 88063 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T14:11:25.647+07:00  INFO 88063 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-07-11T14:11:25.653+07:00  INFO 88063 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-07-11T14:11:25.653+07:00  INFO 88063 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-11T14:11:25.663+07:00  WARN 88063 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-11T14:11:25.831+07:00  INFO 88063 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-11T14:11:26.313+07:00  INFO 88063 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-11T14:11:26.316+07:00  INFO 88063 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-07-11T14:11:26.354+07:00  INFO 88063 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-07-11T14:11:26.399+07:00  INFO 88063 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-07-11T14:11:26.552+07:00  INFO 88063 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-07-11T14:11:26.585+07:00  INFO 88063 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-11T14:11:26.623+07:00  INFO 88063 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 14807704ms : this is harmless.
2025-07-11T14:11:26.632+07:00  INFO 88063 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-07-11T14:11:26.635+07:00  INFO 88063 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-07-11T14:11:26.649+07:00  INFO 88063 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 20903912ms : this is harmless.
2025-07-11T14:11:26.650+07:00  INFO 88063 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-07-11T14:11:26.663+07:00  INFO 88063 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-07-11T14:11:26.664+07:00  INFO 88063 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-07-11T14:11:29.568+07:00  INFO 88063 --- [main] c.d.f.s.message.MessageQueueManager      : Loading 2 messages for session 11/07/2025@14:00:00+0700 to 11/07/2025@14:15:00+0700
2025-07-11T14:11:29.568+07:00  INFO 88063 --- [main] c.d.f.s.message.MessageQueueManager      : 📤 message : ZALO - inquiry-overdue-reminder - scheduled at 11/07/2025@14:10:22+0700
2025-07-11T14:11:29.568+07:00  INFO 88063 --- [main] c.d.f.s.message.MessageQueueManager      : 📤 message : ZALO - inquiry-overdue-reminder - scheduled at 11/07/2025@14:10:31+0700
2025-07-11T14:11:29.568+07:00  INFO 88063 --- [main] c.d.f.s.message.MessageQueueManager      : Loaded 2 messages for session 11/07/2025@14:00:00+0700 to 11/07/2025@14:15:00+0700
2025-07-11T14:11:30.338+07:00  INFO 88063 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-07-11T14:11:30.338+07:00  INFO 88063 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-07-11T14:11:30.338+07:00  WARN 88063 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-07-11T14:11:30.588+07:00  INFO 88063 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-07-11T14:11:30.588+07:00  INFO 88063 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/templates
2025-07-11T14:11:30.588+07:00  INFO 88063 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/datatp-crm/templates
2025-07-11T14:11:30.588+07:00  INFO 88063 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/logistics/templates
2025-07-11T14:11:30.588+07:00  INFO 88063 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-prod/server/addons/document-ie/templates
2025-07-11T14:11:32.086+07:00  WARN 88063 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: a65c1fa2-11dd-4655-9efc-26ce23ae30f6

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-11T14:11:32.089+07:00  INFO 88063 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-11T14:11:32.406+07:00  INFO 88063 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-11T14:11:32.406+07:00  INFO 88063 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-07-11T14:11:32.407+07:00  INFO 88063 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-07-11T14:11:32.407+07:00  INFO 88063 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-11T14:11:32.407+07:00  INFO 88063 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-07-11T14:11:32.407+07:00  INFO 88063 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-07-11T14:11:32.407+07:00  INFO 88063 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-11T14:11:32.407+07:00  INFO 88063 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-07-11T14:11:32.407+07:00  INFO 88063 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-07-11T14:11:32.407+07:00  INFO 88063 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-11T14:11:32.407+07:00  INFO 88063 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-07-11T14:11:32.407+07:00  INFO 88063 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-07-11T14:11:32.410+07:00  INFO 88063 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-11T14:11:32.410+07:00  INFO 88063 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-11T14:11:32.410+07:00  INFO 88063 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-11T14:11:32.482+07:00  INFO 88063 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11T14:11:32.482+07:00  INFO 88063 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-11T14:11:32.484+07:00  INFO 88063 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-07-11T14:11:32.496+07:00  INFO 88063 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@65c84f86{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-11T14:11:32.497+07:00  INFO 88063 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-07-11T14:11:32.499+07:00  INFO 88063 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-07-11T14:11:32.528+07:00  INFO 88063 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-07-11T14:11:32.529+07:00  INFO 88063 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-07-11T14:11:32.536+07:00  INFO 88063 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 15.467 seconds (process running for 15.741)
2025-07-11T14:11:43.015+07:00  INFO 88063 --- [qtp947297132-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-11T14:11:45.017+07:00  INFO 88063 --- [qtp947297132-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01ir32uoxxn6ay1r178kev2zk150
2025-07-11T14:11:45.017+07:00  INFO 88063 --- [qtp947297132-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0mdib9ir79vj61k7auye3mhmnw1
2025-07-11T14:11:45.119+07:00  INFO 88063 --- [qtp947297132-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01ir32uoxxn6ay1r178kev2zk150, token = 43d819da9ef52f9de2b6b5bfe3e094bc
2025-07-11T14:11:45.120+07:00  INFO 88063 --- [qtp947297132-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0mdib9ir79vj61k7auye3mhmnw1, token = 43d819da9ef52f9de2b6b5bfe3e094bc
2025-07-11T14:11:45.532+07:00  INFO 88063 --- [qtp947297132-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T14:11:45.532+07:00  INFO 88063 --- [qtp947297132-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T14:11:46.952+07:00 DEBUG 88063 --- [qtp947297132-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:11:46.952+07:00 DEBUG 88063 --- [qtp947297132-64] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:12:06.479+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:12:35.575+07:00  INFO 88063 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-07-11T14:12:35.619+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:12:35.619+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:12:35.620+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:12:35.620+07:00  INFO 88063 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T14:12:46.930+07:00 DEBUG 88063 --- [qtp947297132-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:13:03.659+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:13:08.666+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:13:08.667+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:13:08.667+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:13:38.719+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:13:38.721+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:13:38.721+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:13:47.694+07:00 DEBUG 88063 --- [qtp947297132-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:14:06.768+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:14:06.772+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:14:06.772+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:14:06.772+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:14:34.858+07:00  INFO 88063 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-07-11T14:14:34.868+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:14:34.868+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:14:34.868+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:14:34.889+07:00  INFO 88063 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-11T14:14:47.673+07:00 DEBUG 88063 --- [qtp947297132-64] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:15:02.941+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-11T14:15:02.943+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T14:15:02.944+07:00  INFO 88063 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 11/07/2025@14:15:02+0700
2025-07-11T14:15:02.977+07:00  INFO 88063 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 0 messages for session 11/07/2025@14:15:00+0700 to 11/07/2025@14:30:00+0700
2025-07-11T14:15:02.978+07:00  INFO 88063 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 0 messages for session 11/07/2025@14:15:00+0700 to 11/07/2025@14:30:00+0700
2025-07-11T14:15:02.978+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:15:08.989+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:15:08.989+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:15:08.990+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:15:09.034+07:00 DEBUG 88063 --- [botTaskExecutor-3] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "11/07/2025 00:00:00 GMT+0700",
      "toDate" : "11/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-11T14:15:38.064+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:15:38.066+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:15:38.066+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:15:47.649+07:00 DEBUG 88063 --- [qtp947297132-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:16:06.116+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:16:06.117+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:16:06.118+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:16:06.118+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:16:39.173+07:00  INFO 88063 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-11T14:16:39.176+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:16:39.176+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:16:39.176+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:16:39.176+07:00  INFO 88063 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T14:16:47.639+07:00 DEBUG 88063 --- [qtp947297132-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:17:02.223+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:17:09.233+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:17:09.233+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:17:09.233+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:17:37.286+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:17:37.287+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:17:37.287+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:18:05.335+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:18:05.336+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:18:05.336+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:18:05.337+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:18:22.627+07:00 DEBUG 88063 --- [qtp947297132-40] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:18:39.396+07:00  INFO 88063 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-07-11T14:18:39.402+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:18:39.402+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:18:39.402+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:18:39.403+07:00  INFO 88063 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-07-11T14:19:06.446+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:19:08.457+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:19:08.458+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:19:08.458+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:19:22.678+07:00 DEBUG 88063 --- [qtp947297132-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:19:36.515+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:19:36.516+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:19:36.516+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:20:04.576+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T14:20:04.578+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:20:04.578+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:20:04.578+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:20:04.579+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:20:22.635+07:00 DEBUG 88063 --- [qtp947297132-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:20:38.633+07:00  INFO 88063 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T14:20:38.635+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:20:38.636+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:20:38.636+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:20:38.637+07:00  INFO 88063 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T14:21:06.684+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:21:07.691+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:21:07.691+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:21:07.692+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:21:22.637+07:00 DEBUG 88063 --- [qtp947297132-41] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:21:35.739+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:21:35.740+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:21:35.741+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:22:03.781+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:22:08.788+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:22:08.789+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node0mdib9ir79vj61k7auye3mhmnw1, remote user nhat.le
2025-07-11T14:22:08.789+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:22:20.012+07:00  INFO 88063 --- [Scheduler-91390948-1] n.d.m.session.AppHttpSessionListener     : The session node0mdib9ir79vj61k7auye3mhmnw1 is destroyed.
2025-07-11T14:22:22.631+07:00  INFO 88063 --- [qtp947297132-40] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-07-11T14:22:38.864+07:00  INFO 88063 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 10
2025-07-11T14:22:38.877+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:22:38.878+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:22:38.878+07:00  INFO 88063 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T14:23:06.922+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:23:06.924+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:23:06.925+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:23:25.642+07:00  INFO 88063 --- [qtp947297132-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01ir32uoxxn6ay1r178kev2zk150, token = 43d819da9ef52f9de2b6b5bfe3e094bc
2025-07-11T14:23:25.663+07:00  INFO 88063 --- [qtp947297132-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T14:23:25.678+07:00  INFO 88063 --- [qtp947297132-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01ir32uoxxn6ay1r178kev2zk150, token = 43d819da9ef52f9de2b6b5bfe3e094bc
2025-07-11T14:23:25.689+07:00  INFO 88063 --- [qtp947297132-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-07-11T14:23:27.780+07:00 DEBUG 88063 --- [qtp947297132-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:23:27.781+07:00 DEBUG 88063 --- [qtp947297132-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:23:34.977+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:23:34.980+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ir32uoxxn6ay1r178kev2zk150, remote user nhat.le
2025-07-11T14:23:34.981+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:24:03.027+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:24:09.036+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:24:09.036+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ir32uoxxn6ay1r178kev2zk150, remote user nhat.le
2025-07-11T14:24:09.036+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:24:28.732+07:00 DEBUG 88063 --- [qtp947297132-69] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:24:38.106+07:00  INFO 88063 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-07-11T14:24:38.118+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:24:38.119+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ir32uoxxn6ay1r178kev2zk150, remote user nhat.le
2025-07-11T14:24:38.119+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:24:38.125+07:00  INFO 88063 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T14:25:06.179+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T14:25:06.180+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:25:06.180+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:25:06.180+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ir32uoxxn6ay1r178kev2zk150, remote user nhat.le
2025-07-11T14:25:06.180+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:25:28.630+07:00 DEBUG 88063 --- [qtp947297132-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:25:39.232+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:25:39.233+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ir32uoxxn6ay1r178kev2zk150, remote user nhat.le
2025-07-11T14:25:39.234+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:26:02.278+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:26:09.293+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:26:09.293+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ir32uoxxn6ay1r178kev2zk150, remote user nhat.le
2025-07-11T14:26:09.294+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:26:28.633+07:00 DEBUG 88063 --- [qtp947297132-69] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:26:37.347+07:00  INFO 88063 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-07-11T14:26:37.350+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:26:37.350+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ir32uoxxn6ay1r178kev2zk150, remote user nhat.le
2025-07-11T14:26:37.350+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:26:37.351+07:00  INFO 88063 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T14:27:05.399+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:27:05.400+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:27:05.400+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ir32uoxxn6ay1r178kev2zk150, remote user nhat.le
2025-07-11T14:27:05.401+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:27:28.637+07:00 DEBUG 88063 --- [qtp947297132-70] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:27:39.459+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:27:39.461+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ir32uoxxn6ay1r178kev2zk150, remote user nhat.le
2025-07-11T14:27:39.461+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:28:06.545+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:28:08.568+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:28:08.569+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ir32uoxxn6ay1r178kev2zk150, remote user nhat.le
2025-07-11T14:28:08.570+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:28:28.649+07:00 DEBUG 88063 --- [qtp947297132-63] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:28:36.676+07:00  INFO 88063 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T14:28:36.679+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:28:36.687+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ir32uoxxn6ay1r178kev2zk150, remote user nhat.le
2025-07-11T14:28:36.687+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:28:36.688+07:00  INFO 88063 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T14:29:04.774+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:29:04.776+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:29:04.777+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ir32uoxxn6ay1r178kev2zk150, remote user nhat.le
2025-07-11T14:29:04.777+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:29:38.869+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:29:38.871+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ir32uoxxn6ay1r178kev2zk150, remote user nhat.le
2025-07-11T14:29:38.871+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:30:06.953+07:00  INFO 88063 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 🔄 Refresh queue at 11/07/2025@14:30:06+0700
2025-07-11T14:30:06.971+07:00  INFO 88063 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loading 1 messages for session 11/07/2025@14:30:00+0700 to 11/07/2025@14:45:00+0700
2025-07-11T14:30:06.971+07:00  INFO 88063 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : 📤 message : ZALO - inquiry-overdue-reminder - scheduled at 11/07/2025@14:36:32+0700
2025-07-11T14:30:06.971+07:00  INFO 88063 --- [scheduling-1] c.d.f.s.message.MessageQueueManager      : Loaded 1 messages for session 11/07/2025@14:30:00+0700 to 11/07/2025@14:45:00+0700
2025-07-11T14:30:06.973+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-07-11T14:30:06.974+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-07-11T14:30:06.974+07:00  INFO 88063 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-07-11T14:30:06.980+07:00 DEBUG 88063 --- [botTaskExecutor-2] n.d.module.bot.BotEventMessageHandler    : handleMessage(...)
{
  "sourceType" : "UserBot",
  "client" : {
    "tenantId" : "default",
    "companyCode" : null,
    "companyId" : null,
    "remoteUser" : "tony.nguyen",
    "accountId" : 0,
    "token" : null,
    "tokenId" : null,
    "remoteIp" : "localhost",
    "sessionId" : null,
    "deviceInfo" : {
      "deviceType" : "Computer"
    },
    "clientId" : "default:tony.nguyen"
  },
  "company" : {
    "id" : -1,
    "code" : "_system_",
    "label" : "System"
  },
  "name" : "task:unit",
  "data" : {
    "client" : {
      "tenantId" : "default",
      "companyCode" : null,
      "companyId" : null,
      "remoteUser" : "tony.nguyen",
      "accountId" : 0,
      "token" : null,
      "tokenId" : null,
      "remoteIp" : "localhost",
      "sessionId" : null,
      "deviceInfo" : {
        "deviceType" : "Computer"
      },
      "clientId" : "default:tony.nguyen"
    },
    "company" : {
      "id" : -1,
      "code" : "_system_",
      "label" : "System"
    },
    "scriptDir" : "/Users/<USER>/nez/code/datatp/working/release-prod/server/addons/core/groovy",
    "scriptFile" : "net/datatp/module/monitor/activity/ActivityLogicExecutor.java",
    "executableUnitName" : "CreateDailyActivityReport",
    "executableUnitClassName" : null,
    "params" : {
      "fromDate" : "11/07/2025 00:00:00 GMT+0700",
      "toDate" : "11/07/2025 23:59:59 GMT+0700"
    },
    "json" : {
      "objectMapper" : { }
    }
  },
  "processMode" : "Queueable",
  "workStatusMap" : { },
  "reportToUsers" : [ "tony.nguyen", "dat.luong" ]
}
2025-07-11T14:30:07.985+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:30:07.985+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ir32uoxxn6ay1r178kev2zk150, remote user nhat.le
2025-07-11T14:30:07.985+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:30:22.642+07:00 DEBUG 88063 --- [qtp947297132-60] n.datatp.module.bot.BackendMockService   : get(...), source type = UserCron, remote user = nhat.le
2025-07-11T14:30:36.072+07:00  INFO 88063 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-07-11T14:30:36.076+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Start: runAutomation()
2025-07-11T14:30:36.076+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : Check jsession node01ir32uoxxn6ay1r178kev2zk150, remote user nhat.le
2025-07-11T14:30:36.076+07:00 DEBUG 88063 --- [scheduling-1] n.d.m.bot.session.SessionBotService      : End: runAutomation()
2025-07-11T14:30:36.076+07:00  INFO 88063 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T14:30:52.711+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@65c84f86{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-07-11T14:30:52.712+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-07-11T14:30:52.712+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-07-11T14:30:52.712+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-07-11T14:30:52.713+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-11T14:30:52.713+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-07-11T14:30:52.713+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-07-11T14:30:52.713+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-07-11T14:30:52.713+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-07-11T14:30:52.713+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-07-11T14:30:52.713+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-07-11T14:30:52.713+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-07-11T14:30:52.713+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-07-11T14:30:52.713+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-07-11T14:30:52.713+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-07-11T14:30:52.713+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-07-11T14:30:52.729+07:00  INFO 88063 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-07-11T14:30:52.798+07:00  INFO 88063 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-07-11T14:30:52.802+07:00  INFO 88063 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-07-11T14:30:52.827+07:00  INFO 88063 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T14:30:52.829+07:00  INFO 88063 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11T14:30:52.829+07:00  INFO 88063 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-07-11T14:30:52.830+07:00  INFO 88063 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-07-11T14:30:52.830+07:00  INFO 88063 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-07-11T14:30:52.970+07:00  INFO 88063 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-07-11T14:30:52.970+07:00  INFO 88063 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-07-11T14:30:52.971+07:00  INFO 88063 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-07-11T14:30:52.971+07:00  INFO 88063 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-07-11T14:30:52.971+07:00  INFO 88063 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-07-11T14:30:52.973+07:00  INFO 88063 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@408f3054{STOPPING}[12.0.15,sto=0]
2025-07-11T14:30:52.976+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-11T14:30:52.978+07:00  INFO 88063 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@734db023{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.16715675871874313929/, jar:file:///Users/<USER>/nez/code/datatp/working/release-prod/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@61774805{STOPPED}}
