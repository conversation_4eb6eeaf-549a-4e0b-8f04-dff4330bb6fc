import React from 'react';
import * as FeatherIcon from 'react-feather'
import { grid, entity, bs, input, util, app } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';
import { app as lgc_app } from '@datatp-ui/logistics';

import { T } from '../backend';
import { AdditionalChargeTarget } from '../../price';
import { ContainerType } from '../../common/ContainerTypeUtil';
import { BBRefBFSOneUnit } from '../../bfsone';

import BBRefNameFeeDesc = lgc_app.logistics.settings.BBRefNameFeeDesc;

export interface UICustomerLocalChargeListProps extends entity.VGridEntityListEditorProps {
  typeOfContainers?: ContainerType[];
  groups?: ('FREIGHT' | 'LCCHARGE' | 'CUS' | 'OTHER' | 'TRUCK')[]
}
export class UICustomerLocalChargeList extends entity.VGridEntityListEditor<UICustomerLocalChargeListProps> {

  constructor(props: UICustomerLocalChargeListProps) {
    super(props);
    const { plugin } = this.props;
    plugin.getModel().getRecords().sort((sel: any) => {
      if (sel['target'] === AdditionalChargeTarget.ORIGIN) return -1;
      else return 1
    });
  }

  onModify = () => {
    const { onModifyBean } = this.props;
    let updatedRecords = this.vgridContext.model.getRecords();
    if (onModifyBean) onModifyBean(updatedRecords, entity.ModifyBeanActions.MODIFY);
  }

  createVGridConfig(): grid.VGridConfig {
    const { appContext, pageContext, typeOfContainers, groups } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();

    const CELL_HEIGHT: number = 40;

    const onInputChange = (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
      if (oldVal !== newVal) {
        const { displayRecord, gridContext } = ctx;
        let record: any = displayRecord.record;
        if (record['unitPrice'] && record['unitPrice'] !== 0) {
          record['quoteRate'] = {}
        }
        // gridContext.getVGrid().forceUpdateView();
        this.onModify()
      }
    }

    let priceFields: string[] = ['quantity', 'unitPrice'];

    let containerTypeFields: grid.FieldConfig[] = [];
    let containers: ContainerType[] = typeOfContainers ? typeOfContainers : []
    for (let type of containers) {
      let fieldName: string | undefined = type.name;
      if (fieldName) {
        priceFields.push(fieldName);

        let fieldConfig: grid.FieldConfig = {
          name: fieldName, label: type.name, width: 130,
          fieldDataGetter: (record: any) => {
            let quoteRate = record['quoteRate']
            return quoteRate[type.name || type.label] || 0;
          },
          listener: {
            onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
              let _rowRecord = cell.getDisplayRecord().record;
              let fieldName: string = event.field.name;
              if (fieldName === 'unitPrice' || fieldName === 'unit') {
                cell.forceUpdate()
              }
            },
          },
          editor: {
            enable: writeCap, type: 'currency',
            renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
              const { tabIndex, focus, displayRecord, fieldConfig } = ctx;
              let record = displayRecord.record;
              let quoteRate: any = record['quoteRate']
              if (!quoteRate[fieldConfig.name]) quoteRate[fieldConfig.name] = quoteRate[type.label] || 0

              return (
                <input.BBCurrencyField bean={quoteRate} precision={3} style={{ height: CELL_HEIGHT }}
                  field={fieldConfig.name} tabIndex={tabIndex} focus={focus}
                  onInputChange={(bean: any, _fieldName: string, oldVal: any, newVal: any) => {
                    if (oldVal !== newVal) {
                      record['unitPrice'] = 0;
                      record['unit'] = '';
                      quoteRate[fieldConfig.name] = newVal
                      onInputChange(record, 'quoteRate', null, quoteRate)
                    }
                  }} />
              );
            },
            onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
              onInputChange(ctx, oldVal, newVal);
              const { displayRecord, fieldConfig, gridContext } = ctx;
              let event: grid.VGridCellEvent = {
                row: displayRecord.row,
                field: fieldConfig,
                event: 'Modified'
              }
              gridContext.broadcastCellEvent(event);
            },
          },
        }
        containerTypeFields.push(fieldConfig)
      }
    }

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: CELL_HEIGHT,
        editor: {
          supportViewMode: ['table'],
          enable: true
        },
        control: {
          width: 40,
          items: [
            {
              name: 'del', hint: 'Delete', icon: FeatherIcon.Trash2,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                ctx.model.removeRecord(dRecord.record);
                ctx.getVGrid().forceUpdateView();
                this.onModify();
              },
            },
            {
              name: 'copy', hint: 'Copy', icon: FeatherIcon.Copy,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let item = dRecord.record;
                let copyItem = JSON.parse(JSON.stringify(item));
                this.vgridContext.model.insertDisplayRecordAt(dRecord.row, copyItem);
                ctx.getVGrid().forceUpdateView();
                this.onModify();
              },
            },
          ]
        },
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          {
            name: 'name', label: 'Description', width: 160, container: 'fixed-left', removable: false,
            editor: {
              type: "string", enable: writeCap, onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { tabIndex, focus, fieldConfig, displayRecord } = ctx;
                let record = displayRecord.record;
                let cssCustom = ''
                if (record['target'] == AdditionalChargeTarget.ORIGIN) {
                  cssCustom += ' text-success';
                } else {
                  cssCustom += ' text-warning';
                }
                return (
                  <BBRefNameFeeDesc type={'SELLING'} minWidth={400} style={{ height: CELL_HEIGHT }} groups={groups}
                    appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} autofocus={focus} required
                    bean={record} beanIdField='code' beanLabelField='name' className={`fw-bold w-100 ${cssCustom}`}
                    hideMoreInfo
                    placeholder='Fee Name' disable={!writeCap}
                    onPostUpdate={(inputUI: React.Component, bean: any, selectOpt: any, userInput: string) => {
                      onInputChange(bean, fieldConfig.name, null, selectOpt);
                      if (inputUI && (inputUI as any).focusInput) {
                        setTimeout(() => {
                          (inputUI as any).focusInput();
                        }, 0);
                      }
                    }}
                  />
                )
              },
            },
            customRender(_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) {
              let record = dRecord.record;
              let val = 'N/A'
              if (record['name']) val = record['name']
              let cssCustom = ''
              if (record['target'] == AdditionalChargeTarget.ORIGIN) {
                cssCustom += ' text-success';
              } else {
                cssCustom += ' text-warning';
              }
              return (<div className={`text-wrap fw-bold ${cssCustom}`}> {val}</div>);
            },
          },
          {
            name: 'currency', label: T('Curr'), width: 80,
            editor: {
              type: "string",
              enable: writeCap,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { tabIndex, focus, fieldConfig, displayRecord } = ctx;
                let record = displayRecord.record;
                return (
                  <module.settings.BBRefCurrency tabIndex={tabIndex} autofocus={focus}
                    style={{ height: CELL_HEIGHT }} placeholder='Curr' minWidth={150}
                    appContext={appContext} pageContext={pageContext} required hideMoreInfo
                    bean={record} beanIdField={fieldConfig.name} onPostUpdate={(_inputUI, _bean, _selectOpt, _userInput) => onInputChange(record, '', null, null)} />
                )
              },
              onInputChange: onInputChange
            },
          },
          ...containerTypeFields,
          {
            name: 'unitPrice', label: T(`Unit Price`), width: 120,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                let fieldName: string = event.field.name;
                if (priceFields.includes(fieldName)) {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'currency', enable: writeCap,
              onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
                onInputChange(ctx, oldVal, newVal);
                const { displayRecord, fieldConfig, gridContext } = ctx;
                let event: grid.VGridCellEvent = {
                  row: displayRecord.row,
                  field: fieldConfig,
                  event: 'Modified'
                }
                gridContext.broadcastCellEvent(event);
              },
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { tabIndex, focus, displayRecord, fieldConfig } = ctx;
                let record = displayRecord.record;
                return (
                  <input.BBCurrencyField bean={record} precision={3} style={{ height: CELL_HEIGHT }}
                    field={fieldConfig.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                )
              }
            },
          },
          {
            name: 'unit', label: T(`Unit`), width: 80,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                let fieldName: string = event.field.name;
                if (priceFields.includes(fieldName)) {
                  cell.forceUpdate()
                }
              },
            },
            editor: {
              type: 'string', enable: writeCap, onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { tabIndex, focus, displayRecord } = ctx;
                let record = displayRecord.record;
                if (record['unitPrice'] && record['unitPrice'] !== 0) {
                  return (
                    <BBRefBFSOneUnit tabIndex={tabIndex} autofocus={focus} minWidth={250}
                      appContext={appContext} pageContext={pageContext} hideMoreInfo style={{ height: CELL_HEIGHT, minWidth: 150 }}
                      bean={record} beanIdField={'unit'} placeholder='Unit'
                      onPostUpdate={(_inputUI, bean, selectOpt, _userInput) => onInputChange(bean, 'unit', null, selectOpt.name)} />
                  )
                } else {
                  return (<div className="flex-hbox justify-content-center" tabIndex={tabIndex} autoFocus={focus} title={"-"}>{"-"}</div>);
                }
              }
            },
          },
          {
            name: 'taxRate', label: T('VAT (%)'), width: 100,
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let item = dRecord.record;
              return (<div>{util.text.formater.percent(item.taxRate)}</div>)
            },
            editor: {
              type: 'percent',
              enable: true,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { tabIndex, focus, displayRecord } = ctx;
                let item = displayRecord.record;
                return (<input.BBPercentField bean={item} style={{ height: CELL_HEIGHT }}
                  field={'taxRate'} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />)
              },
              onInputChange: onInputChange
            },
          },
          {
            name: 'note', label: T(`Notes`), width: 400, style: { height: CELL_HEIGHT },
            editor: { type: 'string', enable: writeCap, onInputChange: onInputChange },
          },
          {
            name: 'code', label: T('Code Ref.'), width: 120, style: { height: CELL_HEIGHT, },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                let _rowRecord = cell.getDisplayRecord().record;
                let fieldName: string = event.field.name;
                if (fieldName === 'name') {
                  cell.forceUpdate()
                }
              },
            },
          },
        ],
      },
      toolbar: { hide: true },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      },
    }
    return config;
  }

  createNewBean() {
    let localCharge = { target: 'ORIGIN', currency: 'USD', quoteRate: {} }
    return localCharge;
  }

  addOrigin = () => {
    let item = { ...this.createNewBean(), target: AdditionalChargeTarget.ORIGIN }
    this.vgridContext.model.addRecord(item);
    grid.initRecordState(item, 0).markNew();
    this.onModify();
    this.forceUpdate();
  }

  addDest = () => {
    let item = { ...this.createNewBean(), target: AdditionalChargeTarget.DESTINATION }
    this.vgridContext.model.addRecord(item);
    grid.initRecordState(item, 0).markNew();
    this.onModify();
    this.forceUpdate();
  }

  onClearPrice = () => {
    const { } = this.props;
    let selectedRecords: Array<grid.DisplayRecord> = this.vgridContext.model.getSelectedDisplayRecords();
    if (selectedRecords.length > 0) {
      this.vgridContext.model.removeSelectedDisplayRecords();
      this.vgridContext.getVGrid().forceUpdateView();
      this.onModify()
    } else {
      bs.dialogConfirmMessage('Clear Prices', 'You want to clear all prices?', () => {
        this.vgridContext.model.update([]);
        this.vgridContext.getVGrid().forceUpdateView();
        this.onModify()
      });
    }
  }

  render() {

    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const hoverStyle: any = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)',
      backgroundColor: lightColor
    };

    return (
      <div className='flex-vbox h-100'>

        <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
          style={{
            minWidth: 900,
            borderColor: borderColor,
            transition: 'all 0.3s ease',
            marginBottom: '10px'
          }}
          onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.backgroundColor = '#fff';
          }}>
          <div className="flex-vbox bg-white rounded-md w-100 h-100">

            <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">
              <h5 style={{ color: '#6c757d' }}><FeatherIcon.Truck className="me-2" size={16} />{`Local Handling Charge (Origin/ Dest)`}</h5>

              <div className="flex-hbox justify-content-end align-items-center">
                <bs.Button laf='success' className="text-decoration-none border-0 py-1 px-1" outline onClick={this.addOrigin}>
                  <FeatherIcon.Plus size={12} /> Add Origin
                </bs.Button>

                <bs.Button laf='warning' className="text-decoration-none border-0 py-1 px-1" outline onClick={this.addDest}>
                  <FeatherIcon.Plus size={12} /> Add Dest
                </bs.Button>

                <bs.Button laf='info' className="text-decoration-none border-0 py-1 px-1" outline onClick={this.onClearPrice}>
                  <FeatherIcon.Trash2 size={12} /> Clear Prices
                </bs.Button>
              </div>
            </div>

            <div className="flex-vbox" >
              <grid.VGrid context={this.vgridContext} />
            </div>

          </div>
        </div>

      </div>
    )
  }
}