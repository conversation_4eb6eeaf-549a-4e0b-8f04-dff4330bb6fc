import React from 'react';
import * as FeatherIcon from 'react-feather';
import { bs, server, util, entity, app } from '@datatp-ui/lib';
import { app as lgc_app } from '@datatp-ui/logistics';

import { T } from '../../backend'
import { UIBookingUtils } from '../../booking/BookingUtils';
import { UIQuotationUtils } from '../QuotationUtils';

import {
  CustomerEntityUtil,
  UIQuotationListEditor,
} from '../../common';
import { UISpecificInquiryForm } from '../../inquiry';

import {
  CalculatorContext,
  calculator,
  CalculatorContextType,
  CalculatorProvider,
  createCalculatorContext,
} from '../../calculator';

import { SQuotationExportProcessor } from './xlsx/UIQuotationExportUtil';
import { DOMESTIC_CHARGES } from '../QuotationConfigDialog';
import { UICustomerLocalChargeList } from '../../common/UICustomerLocalChargeList';
import { ContainerType } from 'app/crm/forwarder/common/ContainerTypeUtil';
import { UIMailRequestPricing } from 'app/crm/forwarder/price';

import TransportationMode = lgc_app.logistics.settings.TransportationMode;
import TransportationTool = lgc_app.logistics.settings.TransportationTool;

export class UISpecificQuotationEditor extends entity.AppDbComplexEntityEditor {
  isModifying: boolean = false;
  selectTab: string = 'price-quote';
  mainViewId = `view-${util.IDTracker.next()}`;
  scrollContainerRef: React.RefObject<HTMLDivElement> = React.createRef();

  getScrollContainerHeight = (): number => {
    return this.scrollContainerRef.current?.clientHeight || 0;
  }

  onNewBooking = () => {
    let { observer, appContext, pageContext } = this.props;
    let quotation = observer.getMutableBean();
    this.onPreCommit(observer)
    let entity = observer.commitAndGet();

    UIBookingUtils.onNewBooking(this, entity);
    entity['editMode'] = 'VALIDATED';

    let commitedEntityCallback = (entity: any) => {
      appContext.addOSNotification('success', "Auto save quotation!!!");
      observer.replaceWithUpdate(entity);
      observer.commitAndGet();
      this.onPostCommit(entity);
      if (pageContext.breadcumbs) pageContext.breadcumbs.forceUpdatePreviousPage();
    };

    appContext
      .createHttpBackendCall('QuotationService', 'saveSpecificQuotation', { entity: entity })
      .withSuccessData(commitedEntityCallback)
      .call()
  }

  onModify = (_quotation: any, field: string, oldVal: any, newVal: any) => {
    let { observer } = this.props;
    if (field.length > 0) {
      observer.replaceBeanProperty(field, newVal);
    } else {
      observer.setMutableBean(_quotation);
    }
    this.isModifying = true;
    observer.commitAndGet();
    this.nextViewId()
    this.forceUpdate();
  }

  onSendQuotation = () => {
    let { appContext, pageContext } = this.props;
    appContext.addOSNotification('warning', "Feature coming soon...");
    return;

    /*
    let mailMessage: string = `
    Dear valued customer,
    On behalf of Bee Logistics, we would like to express our sincere gratitude for your interest in our company's services.
    Please find our service quotation as follows:
    `

    //TODO: Dan - fix it
    const request = { request: UIQuotationUtils.createExportRequest(this), mailMessage: mailMessage };
    appContext.createHttpBackendCall('QuotationService', 'initConfirmQuotationModel', { template: request })
      .withSuccessData((data: any) => {
        const createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (<UIQuotationMailMessage appContext={appCtx} pageContext={pageCtx} observer={new entity.ComplexBeanObserver(data)} />);
        }
        pageContext.createPopupPage("quotation-mail-model", 'Send Quotation', createAppPage, { size: "lg", backdrop: "static" });
      })
      .withFail((response: server.BackendResponse) => {
        let title = T('Generate Email Failed!');
        appContext.addOSNotification('danger', title, response.error, response);
        let message = response.error.message;
        if (message) {
          bs.notificationShow("danger", title, <div className='text-danger'>{message}</div>);
        }
        return;
      })
      .call();
      */
  }

  doExportXlsxQuotation = (clientType: 'CUSTOMER' | 'AGENT' = 'CUSTOMER') => {
    let { observer } = this.props;
    observer.updateMutableBean();
    let quoteSelector = observer.getComplexArrayProperty('quoteListSelector', [])
    let quotation: any = observer.getMutableBean();
    if (quoteSelector.length === 0) {
      quotation['quoteListSelector'] = quotation['quoteList']
    }
    let processor = new SQuotationExportProcessor(quotation, false, clientType);
    UIQuotationUtils.doExportQuoteAsXlsx(this, processor.request);
  }

  onUpdateContext(chargeMargin: any, inquiry: any) {
    let context: CalculatorContextType = this.context as CalculatorContextType;
    let newCxt = createCalculatorContext(chargeMargin, inquiry);
    context.setContext(newCxt);
  }

  onModifyInquiry = (inquiry: any, field: string, _oldVal: any, _newVal: any) => {
    const { observer } = this.props;

    const inquiryObserver = observer.createComplexBeanObserver('inquiry', {});
    if (field.length > 0) {
      inquiryObserver.replaceBeanProperty(field, _newVal);
    } else {
      inquiryObserver.setMutableBean(inquiry);
    }
    const updatedInquiry = inquiryObserver.updateMutableBean();
    observer.replaceBeanProperty('inquiry', updatedInquiry);

    //TODO: Dan - Handle weight/volume updates
    const volumeFields: string[] = ['chargeableVolume', 'volumeCbm', 'chargeableWeight', 'grossWeightKg'];
    if (volumeFields.includes(field)) {
      const volume = field === 'chargeableVolume' ?
        updatedInquiry['chargeableVolume'] :
        (updatedInquiry['volumeCbm'] || 0);

      const weight = field === 'chargeableWeight' ?
        updatedInquiry['chargeableWeight'] :
        updatedInquiry['grossWeightKg'];

      const quoteList = observer.getComplexArrayProperty('quoteList', []);

      const updatedQuoteList = quoteList.map((quote: any) => {
        let localCharges: any[] = quote['localCharges'] || [];
        let updateLocalCharges: any[] = [];

        for (let localCharge of localCharges) {
          if (localCharge['unit'] === 'CBM') {
            localCharge['quantity'] = volume;
            if (localCharge['unit'] === 'KGS' || localCharge['unit'] === 'KGM' || localCharge['unit'] === 'KG') {
              localCharge['quantity'] = weight;
            } else {
              localCharge['quantity'] = 1;
            }
            updateLocalCharges.push(localCharge);
          }
        }

        let newQuote: any = {
          ...quote,
          localCharges: updateLocalCharges,
        }
        const mode: TransportationMode = quote['mode'];
        if (TransportationTool.isAir(mode)) {
          calculator.air.calculatorQuote(newQuote, weight)
        } else if (TransportationTool.isSeaLCL(mode)) {
          calculator.seaLCL.calculatorQuote(newQuote, volume)
        } else if (TransportationTool.isSeaFCL(mode)) {
          calculator.seaFCL.calculatorQuote(newQuote, volume)
        }
        return newQuote;
      });
      observer.replaceBeanProperty('quoteList', updatedQuoteList);

    } else if (field === 'incoterms') {
      let termOfService: string = inquiry['termOfService'];
      if (termOfService === 'DOOR_TO_DOOR' || termOfService === 'DOOR_TO_PORT' || termOfService === 'PORT_TO_DOOR') {
        let customQuote = observer.getComplexBeanProperty('customQuote', {});
        let localCharges: any[] = customQuote['localCharges'] || [];
        if (!localCharges || localCharges.length === 0) {
          customQuote['localCharges'] = DOMESTIC_CHARGES.map(charge => ({
            ...charge,
            target: termOfService === 'PORT_TO_DOOR' ? 'DESTINATION' : 'ORIGIN',
            currency: 'USD',
            quantity: '',
            unitPrice: '',
            totalAmount: '',
            note: '',
            quoteRate: {}
          }));
        }
        observer.replaceBeanProperty('customQuote', customQuote);
      } else {
        observer.replaceBeanProperty('customQuote', {});
      }
    }

    let chargeModel = observer.getComplexBeanProperty('customerChargeModel', {});
    if (!chargeModel || Object.keys(chargeModel).length === 0) {
      chargeModel = CustomerEntityUtil.createDefaultCustomerChargeModel();
    }
    this.onUpdateContext(chargeModel, updatedInquiry);
    this.isModifying = true;
    this.nextViewId();
    this.forceUpdate();
  }

  copyQuotation = () => {
    const { appContext, observer } = this.props;
    let quotation = observer.getMutableBean();
    appContext.createHttpBackendCall('QuotationService', 'copySpecificQuotation', { quotationId: quotation['id'] })
      .withSuccessData((data: any) => {
        appContext.addOSNotification('success', "Copy quotation success!!!");
        UIQuotationUtils.showUISpecificQuotation(this, data);
      })
      .withFail((response: server.BackendResponse) => {
        let title = T('Copy Quotation Failed!');
        appContext.addOSNotification('danger', title, response.error, response);
        let message = response.error.message || title;
        bs.notificationShow("danger", title, <div className='text-danger'>{message}</div>);
        return;
      })
      .call();
  }

  onPricingRequest = () => {
    const { pageContext, observer } = this.props;
    let inquiry = observer.getComplexBeanProperty('inquiry', {});

    let requestModel = {
      mode: inquiry.mode,
      purpose: inquiry.purpose,
      fromLocationCode: inquiry.fromLocationCode,
      fromLocationLabel: inquiry.fromLocationLabel,
      toLocationCode: inquiry.toLocationCode,
      toLocationLabel: inquiry.toLocationLabel,
      cargoReadyDate: inquiry.cargoReadyDate || util.TimeUtil.javaCompactDateTimeFormat(new Date()),
      estimatedTimeDeparture: inquiry.estimatedTimeDeparture,
      termOfService: inquiry.termOfService,
      incoterms: inquiry.incoterms,
      containerTypes: inquiry.containerTypes,
      shipmentDetail: {
        commodity: inquiry.commodity || 'GENERAL',
        volumeInfo: inquiry.containerTypes,
        grossWeightKg: inquiry.grossWeightKg || 0,
        volumeCbm: inquiry.volumeCbm || 0,
        packageQty: inquiry.packageQty || 0,
        descOfGoods: inquiry.descOfGoods || ''
      },
      containers: inquiry.containers || []
    };

    const createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => (
      <div className='flex-hbox'>
        <UIMailRequestPricing appContext={appCtx} pageContext={pageCtx}
          observer={new entity.ComplexBeanObserver(requestModel)}
          onPostCommit={(_entity) => { pageCtx.back(); }}
        />
      </div>
    );

    let type: string = lgc_app.logistics.settings.mapToTypeOfShipment(requestModel.purpose, requestModel.mode) || 'N/A';

    let size: 'flex-lg' | 'xl' = 'flex-lg';
    if (window.innerWidth < 1300) {
      size = 'xl';
    }

    pageContext.createPopupPage('mail-request-pricing', `Request Pricing(${type})`, createPageContent, {
      size: size,
      backdrop: 'static'
    });
  }

  onPreCommit = (observer: entity.ComplexBeanObserver) => {
    if (!observer.getErrorCollector().assertNoError('Quotation Has Error')) return;
    let inquiry = observer.getComplexBeanProperty('inquiry', {});
    observer.commitAndGet();
    observer.updateMutableBean();

    if (!inquiry['fromLocationCode'] || !inquiry['toLocationCode']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide both Port Of Loading and Port Of Discharge .')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      throw new Error('Please provide both Port Of Loading and Port Of Discharge.')
    }

    let gw: number = inquiry['grossWeightKg'] || 0;
    let cbm: number = inquiry['volumeCbm'] || 0;
    let containerTypes: string = inquiry['containerTypes'] || '';
    let mode: TransportationMode = inquiry['mode'];

    if (TransportationTool.isSeaFCL(mode) && !containerTypes) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide Container Types.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      throw new Error('Please provide Container Types.')
    } else if (TransportationTool.isAir(mode) || TransportationTool.isSeaLCL(mode)) {
      if (gw + cbm === 0) {
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide GrossWeight/ CBM.')}
          </div>,
          { backdrop: 'static', size: 'sm' }
        );
        throw new Error('Please provide GrossWeight/ CBM.')
      }
    }
  }

  onPostCommit = (entity: any) => {
    let { onPostCommit } = this.props;
    this.nextViewId();
    this.isModifying = false;
    if (onPostCommit) {
      onPostCommit(entity, this);
    } else {
      this.forceUpdate();
    }
  }

  renderQuoteList(columnH: number) {
    let { appContext, pageContext, observer } = this.props;
    let inquiry: any = observer.getComplexBeanProperty('inquiry', {});
    let mode: TransportationMode = inquiry['mode'];

    const containers: ContainerType[] = (this.context as CalculatorContextType)?.context?.typeOfContainers || []

    const onModifyLocalCharges = (records: Array<any>, _action: any) => {
      for (let record of records) {
        if (!record['mode']) {
          record['mode'] = mode;
        }
        observer.replaceBeanProperty('localHandlingCharges', records);
        this.isModifying = true;
        observer.commitAndGet();
        observer.updateMutableBean();
      }
    }

    const onModifyQuoteList = (records: Array<any>, _action: any) => {
      observer.replaceBeanProperty('quoteList', records);
      this.isModifying = true;
      observer.commitAndGet();
      observer.updateMutableBean();
    }

    let localChargePlugin = observer.createVGridEntityListEditorPlugin('localHandlingCharges', []);

    let quoteListPlugin = observer.createVGridEntityListEditorPlugin('quoteList', []);

    return (
      <>
        <div className='flex-vbox' style={{ height: columnH }}>
          <UIQuotationListEditor
            appContext={appContext} pageContext={pageContext} observer={observer} initContainers={containers}
            dialogEditor={false} editorTitle={''} plugin={quoteListPlugin} onModifyBean={onModifyQuoteList} />
        </div>

        <div className='flex-vbox' style={{ height: (columnH * 2) }}>
          <UICustomerLocalChargeList dialogEditor={false} editorTitle={''}
            appContext={appContext} pageContext={pageContext} typeOfContainers={containers}
            plugin={localChargePlugin} onModifyBean={onModifyLocalCharges} />
        </div>
      </>
    )
  }

  render() {
    let { appContext, pageContext, observer } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    const quotation = observer.getMutableBean();
    let inquiryObserver = observer.createComplexBeanObserver('inquiry', {})
    const quoteContainerH = this.getScrollContainerHeight();
    const columnH = Math.floor(quoteContainerH / 3);
    let minColumnH = Math.max(columnH, 350)
    let editMode = quotation['editMode'] || 'DRAFT';

    return (
      <div className='flex-vbox px-1 my-1 mx-0' key={this.mainViewId}>
        <bs.VSplit updateOnResize>
          <bs.VSplitPane width={400} title={T('Inquiry')} >
            <UISpecificInquiryForm
              appContext={appContext} pageContext={pageContext} observer={inquiryObserver} readOnly={!writeCap}
              onModify={this.onModifyInquiry} />
          </bs.VSplitPane>
          <bs.VSplitPane title={T('Price Quote')} >
            <div className='flex-vbox' key={this.viewId} ref={this.scrollContainerRef}>
              {
                quoteContainerH > 1000 ?
                  this.renderQuoteList(minColumnH)
                  :
                  <bs.GreedyScrollable>
                    {this.renderQuoteList(minColumnH)}
                  </bs.GreedyScrollable>
              }
            </div>
          </bs.VSplitPane>
        </bs.VSplit>

        <bs.Toolbar className='border'>
          <bs.Button laf='warning' className="border-0 p-1" style={{ width: '150px' }}
            onClick={() => this.onPricingRequest()}>
            <FeatherIcon.Mail size={12} /> Request Pricing
          </bs.Button>

          {/* <bs.Button laf='info' className='mx-1 px-2 flex-hbox-grow-0 justify-content-center align-items-center'
            hide={observer.isNewBean()} onClick={this.onSendQuotation} >
            <FeatherIcon.Mail size={12} className='me-1' /> {T('Send Quotation')}
          </bs.Button> */}

          <bs.Popover className='flex-grow-0' closeOnTrigger='.btn'
            placement='top' minWidth={250} minHeight={120} >
            <bs.PopoverToggle className="btn-primary btn-sm">
              <FeatherIcon.Download size={14} className="me-1" /> {T('Export (xlsx)')}
            </bs.PopoverToggle>
            <bs.PopoverContent className="p-2">
              <div className="d-flex flex-column gap-2">


                <bs.Button laf='info' className='d-flex align-items-center justify-content-center px-1 py-2'
                  hide={observer.isNewBean()}
                  onClick={() => this.doExportXlsxQuotation('CUSTOMER')}>
                  <FeatherIcon.Users size={14} className="me-2" /> {T('Customer Format')}
                </bs.Button>

                <bs.Button laf='secondary' className='d-flex align-items-center justify-content-center px-1 py-2'
                  hide={observer.isNewBean()}
                  onClick={() => this.doExportXlsxQuotation('AGENT')}>
                  <FeatherIcon.Briefcase size={14} className="me-2" /> {T('Agent Format')}
                </bs.Button>
              </div>
            </bs.PopoverContent>
          </bs.Popover>

          <bs.Button laf='primary' className='flex-hbox-grow-0 justify-content-center align-items-center'
            // hide={observer.isNewBean()} onClick={this.onNewBooking} disabled={editMode !== 'DRAFT'}>
            hide={observer.isNewBean()} onClick={this.onNewBooking}>
            <FeatherIcon.Plus className='me-1' size={12} /> {T('Internal Booking')}
          </bs.Button>

          <bs.Button laf='info' className='flex-hbox-grow-0 px-1 mx-1'
            hide={observer.isNewBean()} onClick={this.copyQuotation} >
            <FeatherIcon.Copy size={14} className='me-2' /> {T('Save As')}
          </bs.Button>

          <entity.ButtonEntityCommit
            appContext={appContext} pageContext={pageContext}
            hide={!writeCap} observer={observer}
            commit={{
              entityLabel: T('Specific Quotation'),
              context: 'company',
              service: 'QuotationService', commitMethod: 'saveSpecificQuotation'
            }}
            onPostCommit={this.onPostCommit}
            onPreCommit={this.onPreCommit} />

        </bs.Toolbar>
      </div >
    );
  }
}

UISpecificQuotationEditor.contextType = CalculatorContext;

export class UISpecificQuotation extends entity.AppDbComplexEntityEditor {

  render(): React.ReactNode {
    let { observer } = this.props;
    let inquiry = observer.getComplexBeanProperty('inquiry', {});
    let chargeMargin = CustomerEntityUtil.createDefaultCustomerChargeModel();
    let calContext = createCalculatorContext(chargeMargin.transportChargeMargin, inquiry);

    return (
      <CalculatorProvider initContext={calContext}>
        <UISpecificQuotationEditor {...this.props} />
      </CalculatorProvider>
    )
  }

}
