import React from 'react';
import { app, bs, server, entity, util } from '@datatp-ui/lib';
import { app as lgc_app } from '@datatp-ui/logistics';

import { T } from '../backend';

import { UISpecificQuotation } from "./specific/UISpecificQuotation";
import { QuoteExportRequest, } from "./specific/xlsx/UIQuotationExportUtil";
import { UIQuotationMailMessage } from "./UIMailMessage";
import { SQuotationExportProcessor } from "./specific/xlsx/UIQuotationExportUtil";
import { LOCAL_CHARGES } from './QuotationConfigDialog';

import _settings = lgc_app.logistics.settings;
import QuotationSate = _settings.QuotationSate;
import TransportationTool = _settings.TransportationTool;
import TransportationMode = _settings.TransportationMode;


export interface SQuotationCreation {
  priceReferenceIds: Array<number>;
  inquiryRequestId: number | undefined;
  inquiry: any;
}

export class UIQuotationUtils {

  static showUISQuotationById(ui: app.AppComponent, quotationId: number, popup: boolean = false) {
    const { appContext } = ui.props;
    appContext.createHttpBackendCall("QuotationService", "getSpecificQuotationById", { id: quotationId })
      .withSuccessData((quotation: any) => {
        this.showUISpecificQuotation(ui, quotation, '', popup);
      })
      .call()
  }

  static showUISpecificQuotation(ui: bs.BaseComponent, quotation: any, ref: string = '', popup: boolean = false): void {
    let uiAppComp = (ui as app.AppComponent);
    const { pageContext, readOnly } = uiAppComp.props;
    let observer = new entity.ComplexBeanObserver(quotation);
    if (!ref) {
      ref = (quotation['inquiry'] || {})['referenceCode'] || 'NEW QUOTATION';
    }

    let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (<UISpecificQuotation appContext={appCtx} pageContext={pageCtx} readOnly={readOnly} observer={observer} />);
    }
    let pageId = `squotation-detail-${util.IDTracker.next()}`;
    if (popup) {
      pageContext.createPopupPage(pageId, T(`SQuotation : ${ref}`), createPageContent, { size: 'xl', backdrop: 'static' });
    } else {
      pageContext.addPageContent(pageId, `${ref}`, createPageContent);
    }
  }

  // GENERIC QUOTATION
  static showUIGenericQuotationByCode(ui: app.AppComponent, quotationCode: string, popup: boolean = false) {
    let { appContext } = ui.props;

    appContext.createHttpBackendCall('QuotationService', 'getGenericQuotationByCode', { code: quotationCode })
      .withSuccessData((data: any) => {
        let quotation = data;
        this.showUIGenericQuotation(ui, quotation);
      })
      .call()
  }

  static showUIGenericQuotationByID(ui: app.AppComponent, quoteId: number) {
    let { appContext } = ui.props;

    appContext.createHttpBackendCall('QuotationService', 'getGenericQuotationById', { id: quoteId })
      .withSuccessData((data: any) => {
        let quotation = data;
        this.showUIGenericQuotation(ui, quotation);
      })
      .call()
  }

  static showUIGenericQuotation(ui: app.AppComponent, quote: any) {
    let { pageContext } = ui.props;
    let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (<div>TODO</div>);
    }
    pageContext.addPageContent('combined-quote', `${T('Combined Quote')} [${quote['code']}]`, createPageContent);
  }

  static doExportQuoteAsXlsx(ui: bs.BaseComponent, request: QuoteExportRequest) {
    const { appContext } = (ui as app.AppComponent).props;
    appContext.createHttpBackendCall("QuotationDocumentService", "exportQuotationQuoteAsXlsx", { req: request })
      .withSuccessData((storeInfo: any) => {
        if (storeInfo.privateMode) {
          entity.StoreInfo.privateDownload(storeInfo);
        } else {
          entity.StoreInfo.download(storeInfo.url);
        }
      })
      .withFail((response?: server.BackendResponse) => {
        bs.notificationShow("danger", T("Export Quotation Failed!!"), response?.error.message);
        return;
      })
      .call();
  }

  static isGenericQuotation(quotation: any) {
    const isGenericQuotation: boolean = quotation.inquiry.transportation ? true : false;
    return isGenericQuotation;
  }

  static canEdit(quotationState: QuotationSate): boolean {
    if ([QuotationSate.DRAFT, QuotationSate.PENDING].includes(quotationState)) return true;
    else return false;
  }

  static createExportRequest(uiQuotation: entity.AppDbComplexEntityEditor): QuoteExportRequest {
    const { observer } = uiQuotation.props;
    let quotation = observer.getMutableBean();
    let processor = new SQuotationExportProcessor(quotation);
    return processor.request;
  }

  static showUIEmailClient(uiQuotation: entity.AppDbComplexEntityEditor) {
    const { pageContext } = uiQuotation.props;
    const request = this.createExportRequest(uiQuotation)
    const createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (<UIQuotationMailMessage appContext={appCtx} pageContext={pageCtx} observer={new entity.ComplexBeanObserver(request)} />);
    }
    pageContext.createPopupPage("message-editor", 'Mail Client', createAppPage, { size: "flex-lg", backdrop: "static" });
  }


  static createNewSpecificQuotation(ui: app.AppComponent, template: SQuotationCreation, successCb?: (quotation: any) => void): void {
    const { appContext } = ui.props;
    appContext.createHttpBackendCall('QuotationService', 'newSpecificQuotation', { template: template })
      .withSuccessData((quotation: any) => {
        //TODO: Dan - hardcode to fix bugs
        quotation['editMode'] = 'DRAFT';

        appContext.addOSNotification('success', "Create quotation success!!!");
        if (successCb) {
          successCb(quotation);
        } else {
          let inquiry: any = quotation['inquiry'];
          let mode: TransportationMode = inquiry['mode'];
          let purpose: 'IMPORT' | 'EXPORT' = inquiry['purpose'];

          if (TransportationTool.isSea(mode)) {
            const key = mode + '_' + purpose;
            const localCharges: any[] = LOCAL_CHARGES[key]
              .map(charge => ({
                ...charge,
                mode,
                currency: 'USD',
                quantity: '',
                unitPrice: '',
                totalAmount: '',
                note: '',
                quoteRate: {}
              }));
            let existingLocalCharges: any[] = quotation['localHandlingCharges'] || [];
            const existingCodes = new Set(existingLocalCharges.map(charge => charge.code));
            const newLocalCharges = localCharges.filter(charge => !existingCodes.has(charge.code));
            quotation['localHandlingCharges'] = [...existingLocalCharges, ...newLocalCharges];
          }

          UIQuotationUtils.showUISpecificQuotation(ui, quotation);
        }
      })
      .withFail((response: server.BackendResponse) => {
        let title = T('Create Quotation Failed!');
        appContext.addOSNotification('danger', title, response.error, response);
        let message = response.error.message;
        if (message) {
          bs.notificationShow("danger", title, <div className='text-danger'>{message}</div>);
        }
        return;
      })
      .call();
  }

}
