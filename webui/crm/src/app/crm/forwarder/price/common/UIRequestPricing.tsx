import React, { createRef, RefObject } from 'react';
import * as FeatherIcon from "react-feather";

import { bs, input, app, util, entity, component } from '@datatp-ui/lib';
import { module } from "@datatp-ui/erp";
import { app as lgc_app } from '@datatp-ui/logistics';

import { ImportExportPurpose, TransportationMode } from '../../model';
import { BBContainerType } from '../../common/BBContainerInput';
import { BBRefBFSOnePartner } from '../../bfsone';
import { ContainerTypeUnit } from '../../common/ContainerTypeUtil';

import { T } from '../backend';

import _settings = lgc_app.logistics.settings;

const SESSION = app.host.DATATP_HOST.session;

const { POSITIVE_NUMBER_VALIDATOR } = util.validator;

import BBRefLocation = module.settings.BBRefLocation;

const MAIL_SETTINGS = new Map([
  ['SEA_FCL_VNHPH', { label: '<EMAIL>', mail: '<EMAIL>' }],
  ['SEA_FCL_VNSGN', { label: '<EMAIL>', mail: '<EMAIL>' }],
  ['SEA_FCL_VNDAD', { label: '<EMAIL>', mail: '<EMAIL>' }],
  ['SEA_FCL_VNUIH', { label: '<EMAIL>', mail: '<EMAIL>' }],
  ['SEA_LCL_VNHPH', { label: '<EMAIL>', mail: '<EMAIL>' }],
  ['SEA_LCL_VNSGN', { label: '<EMAIL>', mail: '<EMAIL>' }],
]);

export const TEXT_STYLE: any = {
  paragraph: {
    lineHeight: '1.2',
    color: '#2c3e50',
    margin: '0',
    padding: '0',
  },
  greeting: {
    color: '#1a365d',
    marginBottom: '15px',
    fontStyle: 'italic',
    letterSpacing: '0.3px',
  },
  introduction: {
    marginBottom: '10px',
    color: '#2d3748',
    fontWeight: 500 as const,
  },
  link: {
    color: '#2563eb',
    textDecoration: 'none',
    fontWeight: 500 as const,
    padding: '2px 4px',
    borderRadius: '4px',
    transition: 'all 0.2s ease',
    ':hover': {
      backgroundColor: '#eff6ff',
      textDecoration: 'underline',
    }
  }
};

export const TABLE_STYLE: any = {
  table: {
    borderCollapse: 'collapse' as const,
    borderSpacing: 0,
    overflowY: 'scroll' as const,
    overflowX: 'scroll' as const,
    tableLayout: 'fixed' as const,
    width: 'max-content',
    minWidth: '800px',
    margin: '30px 0',
  },
  row: {
    minWidth: '800px',
  },
  cell: {
    border: '1px solid #bbb',
    minWidth: '150px',
    verticalAlign: 'top',
    textAlign: 'start' as const,
    padding: '2px 4px',
    position: 'relative' as const,
    outline: 'none',
    backgroundColor: '#ffffff',
    lineHeight: 1.2,
    wordWrap: 'break-word' as const,
    transition: 'background-color 0.2s ease',
  },
  headerCell: {
    backgroundColor: '#f2f3f5',
  },
  paragraph: {
    margin: 0,
    lineHeight: 1.2,
    padding: '4px 0',
    textAlign: 'left',
    minHeight: '20px',
  },
  lineText: {
    display: 'inline-block',
    padding: '2px 0',
  }
};

interface UIRequestPricingMailMessageProps extends entity.AppComplexEntityEditorProps { }
export class RequestPricingMailTemplate extends entity.AppDbComplexEntityEditor<UIRequestPricingMailMessageProps> {

  renderRowField = (name: string, value: any) => {
    if (!value) value = '';
    const nameLines = name.split('\n').length;

    let valueLines = 1;
    if (typeof value === 'string') {
      valueLines = value.split('\n').length;
    }
    const maxLines = Math.max(nameLines, valueLines);
    const height = Math.max(20, maxLines * 20); // Minimum 20px, add 20px per line

    const rowStyle = {
      ...TABLE_STYLE.row,
      height: `${height}px`,
    };

    const cellStyle = {
      ...TABLE_STYLE.cell,
    };

    const headerCellStyle = {
      ...TABLE_STYLE.cell,
      ...TABLE_STYLE.headerCell,
    };

    return (
      <tr style={rowStyle}>
        <td colSpan={6} style={headerCellStyle}>
          <p style={TABLE_STYLE.paragraph}>
            {name.split('\n').map((line, i) => (
              <React.Fragment key={i}>
                {i > 0 && <br />}
                <span style={TABLE_STYLE.lineText}>{line}</span>
              </React.Fragment>
            ))}
          </p>
        </td>
        <td colSpan={6} style={{ ...cellStyle, width: '50%' }}>
          <p style={TABLE_STYLE.paragraph}>
            {typeof value !== 'string'
              ? value
              : value.split('\n').map((line: string, i: number) => (
                <React.Fragment key={i}>
                  {i > 0 && <br />}
                  <span style={{
                    ...TABLE_STYLE.lineText,
                    color: line === '[Yes]' ? '#000000' : 'inherit',
                    fontWeight: line === '[Yes]' ? 600 : 'normal'
                  }}>{line}</span>
                </React.Fragment>
              ))}
          </p>
        </td>
      </tr>
    )
  }

  renderRowData = (val: string) => {
    if (!val) val = '';
    const valLines = val.split('\n').length;
    const height = Math.max(20, valLines * 20); // Minimum 20px, add 20px per line

    const rowStyle = {
      ...TABLE_STYLE.row,
      height: `${height}px`,
    };

    return (
      <tr style={rowStyle}>
        <td colSpan={12} style={{ ...TABLE_STYLE.cell }} >
          <p style={TABLE_STYLE.paragraph}>
            {val.split('\n').map((line, i) => (
              <React.Fragment key={i}>
                {i > 0 && <br />}
                <span>{line}</span>
              </React.Fragment>
            ))}
          </p>
        </td>
      </tr>
    )
  }

  renderRowHeading = (name: string) => {
    const headerCellStyle = {
      ...TABLE_STYLE.cell,
      backgroundColor: 'rgb(245, 129, 35)',
      border: '1px solid black',
      verticalAlign: 'center',
      textAlign: 'start'
    };

    const rowStyle = {
      ...TABLE_STYLE.row,
      height: `20px`,
    };

    return (
      <tr style={rowStyle}>
        <th colSpan={12} style={headerCellStyle}>
          <p style={TABLE_STYLE.paragraph} dir="ltr">
            <span>{name}</span>
          </p>
        </th>
      </tr>
    )
  }


  renderFCLSection = () => {
    const { observer } = this.props;
    let requestPricing = observer.getMutableBean();
    let mode: TransportationMode = requestPricing['mode'];
    if (!_settings.TransportationTool.isSeaFCL(mode)) return <></>
    let shipmentDetail = requestPricing['shipmentDetail'] || {};
    let specialReq: string = shipmentDetail['specialRequestNote'];
    let customerLabel = requestPricing['clientLabel'];
    let isDGLiquidCargo: string = shipmentDetail['dgLiquidCargo'] ? `[Yes]` : '[No]';
    let isBuyInsurance: string = shipmentDetail['buyInsuranceRequest'] ? `[Yes]` : '[No]';

    let cargoReadyDate: string = requestPricing['cargoReadyDate']
      ? util.TimeUtil.toCompactDateFormat(util.TimeUtil.parseCompactDateTimeFormat(requestPricing['cargoReadyDate']))
      : util.TimeUtil.toCompactDateFormat(new Date());

    let commodity: string = shipmentDetail['commodity'] || '';
    if (commodity) commodity += '\n ';
    commodity += shipmentDetail['descOfGoods'] || '';

    return (
      <>
        {this.renderRowHeading('General Information')}
        {this.renderRowField('Loading port', `${requestPricing['fromLocationCode']}  - ${requestPricing['fromLocationLabel']}`)}
        {this.renderRowField('Destination port', `${requestPricing['toLocationCode']}  - ${requestPricing['toLocationLabel']}`)}
        {this.renderRowField('Commodity', `${commodity}`)}
        {this.renderRowField('Container Types', shipmentDetail['volumeInfo'])}
        {this.renderRowField('G.W (KG)', shipmentDetail['grossWeightKg'])}
        {this.renderRowField('VOL (CBM)', shipmentDetail['volumeCbm'])}
        {this.renderRowField('Cargo Ready Date', cargoReadyDate)}
        {specialReq && this.renderRowField('Special container: \n Provide Temperature (If reefer container), DIM, GW, Picture of goods', specialReq)}
        {this.renderRowField('Term of delivery', requestPricing['termOfService'])}
        {this.renderRowField('Request to buy insurance?', isBuyInsurance)}
        {this.renderRowField('DG/ Liquid cargo?', isDGLiquidCargo)}
        {this.renderRowField('Request for free time/terminal at dest', shipmentDetail['freeTimeTerminalRequest'])}

        {requestPricing['targetRate'] && (
          <>
            {this.renderRowHeading('Target rate')}
            {this.renderRowField('Rate idea (If any)', requestPricing['targetRate'])}
          </>
        )}

        {requestPricing['note'] && (
          <>
            {this.renderRowHeading('Note')}
            {this.renderRowData(requestPricing['note'])}
          </>
        )}
        {this.renderRowHeading('DOOR Delivery')}
        {customerLabel && this.renderRowField('Customer/ Lead', requestPricing['clientLabel'])}
        {this.renderRowField('Pickup address (If Exw/FCA terms)', requestPricing['pickupAddress'])}
        {this.renderRowField('Delivery address', requestPricing['deliveryAddress'])}
      </>
    )
  }

  renderLCLSection = () => {
    const { observer } = this.props;
    let requestPricing = observer.getMutableBean();
    let mode: TransportationMode = requestPricing['mode'];
    if (!_settings.TransportationTool.isSeaLCL(mode)) return <></>
    let shipmentDetail = requestPricing['shipmentDetail'] || {};
    let dim: string = `${shipmentDetail['dimensionL']}x${shipmentDetail['dimensionW']}x${shipmentDetail['dimensionH']}`;
    return (
      <>
        {this.renderRowHeading('Ocean Freight (LCL)')}
        {this.renderRowField('DIM: LxWxH (cm)', dim)}
        {this.renderRowField('No. of package', shipmentDetail['packageQty'])}
        {this.renderRowField('VOL (CBM)', shipmentDetail['volumeCbm'])}
        {this.renderRowField('G.W (KG)', shipmentDetail['grossWeightKg'])}
        {this.renderRowField('Stackable or Non Stackable?', shipmentDetail['stackable'])}
        {this.renderRowField('Special request: keep cool, degree?', shipmentDetail['specialRequestNote'])}
      </>
    )
  }

  renderAirSection = () => {
    const { observer } = this.props;
    let requestPricing = observer.getMutableBean();
    let mode: TransportationMode = requestPricing['mode'];
    if (!_settings.TransportationTool.isAir(mode)) return <></>
    let shipmentDetail = requestPricing['shipmentDetail'] || {};
    let label = shipmentDetail['expressCourier'] ? 'Express Courier' : 'Air Freight';
    let dim: string = `${shipmentDetail['dimensionL']}x${shipmentDetail['dimensionW']}x${shipmentDetail['dimensionH']}`;

    return (
      <>
        {this.renderRowHeading(label)}
        {this.renderRowField('DIM: LxWxH (cm)', dim)}
        {this.renderRowField('No. of package', shipmentDetail['packageQty'])}
        {this.renderRowField('VOL (CBM)', shipmentDetail['volumeCbm'])}
        {this.renderRowField('G.W (KG)', shipmentDetail['grossWeightKg'])}
        {this.renderRowField('Stackable or Non Stackable?', shipmentDetail['stackable'])}
        {this.renderRowField('Special request: keep cool, degree?', shipmentDetail['specialRequestNote'])}
      </>
    )
  }

  renderTruckingSection = () => {
    const { observer } = this.props;
    let requestPricing = observer.getMutableBean();
    let mode: TransportationMode = requestPricing['mode'];
    if (!_settings.TransportationTool.isTruck(mode)) return <></>
    let header = _settings.TransportationTool.isTruckContainer(mode) ? 'Trucking (FCL)' : 'Trucking (LCL)'

    let shipmentDetail = requestPricing['shipmentDetail'] || {};
    let dim: string = `${shipmentDetail['dimensionL']}x${shipmentDetail['dimensionW']}x${shipmentDetail['dimensionH']}(cm)`;
    return (
      <>
        {this.renderRowHeading(header)}
        {this.renderRowField('DIM: LxWxH (cm)', dim)}
        {this.renderRowField('VN Border Gate', shipmentDetail['vnBorderGateRequest'])}
      </>
    )
  }

  render(): React.ReactNode {
    const { observer } = this.props;
    let requestPricing = observer.getMutableBean();

    let shipmentDetail: any = requestPricing['shipmentDetail'];

    let mode: TransportationMode = requestPricing['mode'];
    let purpose: ImportExportPurpose = requestPricing['purpose'];
    let typeOfShipment: string = _settings.mapToTypeOfShipment(purpose, mode);
    let isDGLiquidCargo: string = shipmentDetail['dgLiquidCargo'] ? `[Yes]` : '[No]';
    let isBuyInsurance: string = shipmentDetail['buyInsuranceRequest'] ? `[Yes]` : '[No]';

    let cargoReadyDate: string = requestPricing['cargoReadyDate']
      ? util.TimeUtil.toCompactDateFormat(util.TimeUtil.parseCompactDateTimeFormat(requestPricing['cargoReadyDate']))
      : util.TimeUtil.toCompactDateFormat(new Date());

    let commodity: string = shipmentDetail['commodity'] || '';
    if (commodity) commodity += '\n ';
    commodity += shipmentDetail['descOfGoods'] || '';

    let customerLabel = requestPricing['clientLabel'];
    let pickupAddress = requestPricing['pickupAddress'];
    let deliveryAddress = requestPricing['deliveryAddress'];

    return (
      <div className='flex-vbox container-fluid'>

        <p style={{ ...TEXT_STYLE.paragraph, ...TEXT_STYLE.greeting }}>
          <span style={{ fontWeight: 500 }}>Dear Pricing Team,</span>
        </p>

        <p style={{ ...TEXT_STYLE.paragraph, ...TEXT_STYLE.introduction }}>
          Please kindly help to check the below inquiry:
        </p>

        <p style={{ ...TEXT_STYLE.paragraph, ...TEXT_STYLE.introduction }}>
          After checking the pricing information, please
          <a
            href="https://beelogistics.cloud"
            style={TEXT_STYLE.link}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#eff6ff';
              e.currentTarget.style.textDecoration = 'underline';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.textDecoration = 'none';
            }}
          >
            {' '}click here{' '}
          </a>
          to confirm and notify the requester.
        </p>

        <table style={TABLE_STYLE.table}>
          <tbody>

            {
              _settings.TransportationTool.isSeaFCL(mode)
                ? <>
                  {this.renderFCLSection()}
                </>
                :
                <>
                  {this.renderRowHeading('General Information')}
                  {customerLabel && this.renderRowField('Customer/ Lead', requestPricing['clientLabel'])}
                  {pickupAddress && this.renderRowField('Pickup address (If Exw/FCA terms)', requestPricing['pickupAddress'])}
                  {deliveryAddress && this.renderRowField('Delivery address', requestPricing['deliveryAddress'])}
                  {this.renderRowField('Commodity', `${commodity}`)}
                  {this.renderRowField('DG/ Liquid cargo?', isDGLiquidCargo)}
                  {this.renderRowField('Term of delivery', requestPricing['termOfService'])}
                  {this.renderRowField('Mode of service', typeOfShipment)}
                  {_settings.TransportationTool.isTruckRegular(mode) && this.renderRowField('Truck Type', shipmentDetail['volumeInfo'])}
                  {_settings.TransportationTool.isTruckContainer(mode) && this.renderRowField('Container Type', shipmentDetail['volumeInfo'])}
                  {!_settings.TransportationTool.isTruck(mode) && (
                    <>
                      {this.renderRowField('Loading port / airport', `${requestPricing['fromLocationCode']}  - ${requestPricing['fromLocationLabel']}`)}
                      {this.renderRowField('Destination port / airport', `${requestPricing['toLocationCode']}  - ${requestPricing['toLocationLabel']}`)}
                    </>
                  )}
                  {this.renderRowField('Cargo Ready Date', cargoReadyDate)}
                  {this.renderRowField('Request to buy insurance?', isBuyInsurance)}
                  {this.renderLCLSection()}
                  {this.renderAirSection()}
                  {this.renderTruckingSection()}

                  {requestPricing['targetRate'] && (
                    <>
                      {this.renderRowHeading('Target rate')}
                      {this.renderRowField('Rate idea (If any)', requestPricing['targetRate'])}
                    </>
                  )}

                  {requestPricing['note'] && (
                    <>
                      {this.renderRowHeading('Note')}
                      {this.renderRowData(requestPricing['note'])}
                    </>
                  )}
                </>
            }
          </tbody>
        </table>

        <div style={{
          marginTop: '10px',
          padding: '20px',
          borderLeft: '3px solid rgb(245,129,35)',
          backgroundColor: 'rgb(248,249,250)'
        }}>
          <p style={{
            fontSize: '16px',
            color: 'rgb(44,62,80)',
            marginBottom: '12px',
            fontWeight: 600
          }}>{requestPricing['salemanLabel'] || SESSION.getAccountAcl().getFullName()}</p>
          <div style={{
            fontSize: '14px',
            color: 'rgb(52,73,94)',
            lineHeight: 1.6
          }}>
            <p style={{ marginBottom: '8px' }}>
              <span style={{ fontWeight: 500 }}>{requestPricing['salemanJobTitle'] || 'Sale Executive'}</span>
              <br />
              <span style={{
                color: 'rgb(245,129,35)',
                fontWeight: 500
              }}>Bee Logistics Corporation</span>
            </p>
            <p style={{ marginBottom: '15px' }}>
              <span style={{
                display: 'inline-block',
                width: '14px',
                height: '14px',
                marginRight: '8px',
                backgroundColor: 'rgb(245,129,35)',
                WebkitMask: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath d='M497.39 361.8l-112-48a24 24 0 0 0-28 6.9l-49.6 60.6A370.66 370.66 0 0 1 130.6 204.11l60.6-49.6a23.94 23.94 0 0 0 6.9-28l-48-112A24.16 24.16 0 0 0 122.6.61l-104 24A24 24 0 0 0 0 48c0 256.5 207.9 464 464 464a24 24 0 0 0 23.4-18.6l24-104a24.29 24.29 0 0 0-14.01-27.6z'/%3E%3C/svg%3E") center/contain no-repeat`,
                mask: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath d='M497.39 361.8l-112-48a24 24 0 0 0-28 6.9l-49.6 60.6A370.66 370.66 0 0 1 130.6 204.11l60.6-49.6a23.94 23.94 0 0 0 6.9-28l-48-112A24.16 24.16 0 0 0 122.6.61l-104 24A24 24 0 0 0 0 48c0 256.5 207.9 464 464 464a24 24 0 0 0 23.4-18.6l24-104a24.29 24.29 0 0 0-14.01-27.6z'/%3E%3C/svg%3E") center/contain no-repeat`
              }} />
              <span>Mobile: {requestPricing['salemanPhone'] || 'N/A'}</span>
              <br />
              <span style={{
                display: 'inline-block',
                width: '14px',
                height: '14px',
                marginRight: '8px',
                backgroundColor: 'rgb(245,129,35)',
                WebkitMask: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath d='M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7 22.4 17.4 52.1 39.5 154.1 113.6 21.1 15.4 56.7 47.8 92.2 47.6 35.7.3 72-32.8 92.3-47.6 102-74.1 131.6-96.3 154-113.7zM256 320c23.2.4 56.6-29.2 73.4-41.4 132.7-96.3 142.8-104.7 173.4-128.7 5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9 30.6 23.9 40.7 32.4 173.4 128.7 16.8 12.2 50.2 41.8 73.4 41.4z'/%3E%3C/svg%3E") center/contain no-repeat`,
                mask: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath d='M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7 22.4 17.4 52.1 39.5 154.1 113.6 21.1 15.4 56.7 47.8 92.2 47.6 35.7.3 72-32.8 92.3-47.6 102-74.1 131.6-96.3 154-113.7zM256 320c23.2.4 56.6-29.2 73.4-41.4 132.7-96.3 142.8-104.7 173.4-128.7 5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9 30.6 23.9 40.7 32.4 173.4 128.7 16.8 12.2 50.2 41.8 73.4 41.4z'/%3E%3C/svg%3E") center/contain no-repeat`
              }} />
              <span>Email: {requestPricing['salemanEmail']}</span>
            </p>
          </div>
          <p className="mt-4 mb-0" style={{
            color: '#e67e22',
            fontWeight: 600,
            fontStyle: 'italic',
            fontSize: '14px',
            letterSpacing: '0.5px'
          }}>
            Thanks & Best Regards!
          </p>
        </div>
      </div>
    )
  }

}

export function onMapEmail(request: any) {
  let to: string[] = request['to'] || [];
  let mailToBeans: any[] = [];
  for (let sel of to) {
    if (!sel) return;
    mailToBeans.push({ label: sel, email: sel })
  }
  request['mailToBeans'] = mailToBeans

  let cc: string[] = request['cc'] || [];
  let mailCcBeans: any[] = [];
  for (let sel of cc) {
    if (!sel) return;
    mailCcBeans.push({ label: sel, email: sel })
  }
  request['mailCcBeans'] = mailCcBeans
  return request;
}

interface UIRequestPricingProps extends entity.AppComplexEntityEditorProps { }
export class UIRequestPricing extends entity.AppDbComplexEntityEditor<UIRequestPricingProps> {
  attachmentsSize = 0;
  containerRef: RefObject<HTMLDivElement>;
  mailSetting = MAIL_SETTINGS;

  constructor(props: UIRequestPricingProps) {
    super(props);
    this.containerRef = createRef<HTMLDivElement>();
    const { observer } = this.props;
    let requestModel = observer.getMutableBean();
    let mode: TransportationMode = requestModel['mode'];

    if (_settings.TransportationTool.isSea(mode)) {
      let portOfLoading = requestModel['fromLocationCode'];
      const key = `${mode}_${portOfLoading}`;
      if (this.mailSetting.has(key)) {
        let mail: any = this.mailSetting.get(key);
        // Filter out any existing emails that match with mailSetting emails
        let mailTo: any[] = (requestModel['mailToBeans'] || [])
          .filter((item: any) => !Array.from(this.mailSetting.values())
            .some(setting => setting.mail === item.email));

        // Add new email from mailSetting
        mailTo = [...mailTo, { label: mail['label'], email: mail['mail'] }];
        requestModel['mailToBeans'] = mailTo;
        observer.setMutableBean(requestModel);
      }
    }

  }

  onModify = (bean: any, _field: string, _oldVal: any, newVal: any) => {
    const { observer } = this.props;
    let requestModel = observer.getMutableBean();
    if (_field === 'fromLocationCode') {
      let mode: TransportationMode = requestModel['mode'];
      if (_settings.TransportationTool.isSea(mode)) {
        const key = `${mode}_${newVal}`;

        if (this.mailSetting.has(key)) {
          let mail: any = this.mailSetting.get(key);
          // Filter out any existing emails that match with mailSetting emails
          let mailTo: any[] = (requestModel['mailToBeans'] || [])
            .filter((item: any) => !Array.from(this.mailSetting.values())
              .some(setting => setting.mail === item.email));
          // Add new email from mailSetting
          mailTo = [...mailTo, { label: mail['label'], email: mail['mail'] }];
          requestModel['mailToBeans'] = mailTo;
          observer.setMutableBean(requestModel);
        }
      }
    }

    if ((_field === 'mailCcBeans' || _field === 'mailToBeans') && newVal && Object.keys(newVal).length > 0) {
      const emailList: any[] = bean[_field] || [];
      const displayName = newVal['fullName'] || newVal['zaloDisplayName'] || newVal['loginId'];
      const emailAddress = newVal['email'];
      const label = `${displayName} (${emailAddress})`;

      if (displayName) {
        // Filter out any items with an id property that has a value
        const filteredList = emailList.filter(item => !item.id);
        filteredList.push({ label: label, email: emailAddress });
        bean[_field] = filteredList;
      }
    }
    this.nextViewId();
    this.forceUpdate();
  }

  onModifyShipmentDetail = (bean: any, field: string, _oldVal: any, newVal: any) => {
    const { observer } = this.props;
    if (field === 'dgLiquidCargo') {
      bean['commodity'] = newVal ? 'DG' : 'GENERAL';
    }
    observer.replaceBeanProperty('shipmentDetail', bean);
    this.nextViewId();
    this.forceUpdate();
  }

  renderDimensions = () => {
    const { observer } = this.props;
    let shipmentDetail = observer.getBeanProperty('shipmentDetail', {});
    return (
      <div className='flex-vbox'>
        <bs.Row>

          <bs.Col span={2}>
            <bs.CssTooltip position='top-right' width={400} offset={{ x: 400, y: 0 }}>
              <bs.CssTooltipToggle className='flex-hbox'>
                <input.BBNumberField label={T('DIM (L) (cm)')} bean={shipmentDetail} field={'dimensionL'}
                  onInputChange={this.onModifyShipmentDetail} />
              </bs.CssTooltipToggle>
              <bs.CssTooltipContent className="d-flex flex-column rounded">
                <div className="tooltip-header mb-2">
                  <span className="tooltip-title">Lưu ý khi nhập kích thước</span>
                </div>
                <div className="tooltip-body text-secondary">
                  Trong trường hợp có nhiều pallet/packages với kích thước khác nhau, vui lòng mô tả chi tiết trong phần "Description of goods".
                </div>
              </bs.CssTooltipContent>
            </bs.CssTooltip>
          </bs.Col>

          <bs.Col span={2}>
            <input.BBNumberField label={T('DIM (W) (cm)')} bean={shipmentDetail} field={'dimensionW'}
              onInputChange={this.onModifyShipmentDetail} />
          </bs.Col>
          <bs.Col span={2}>
            <input.BBNumberField label={T('DIM (H) (cm)')} bean={shipmentDetail} field={'dimensionH'}
              onInputChange={this.onModifyShipmentDetail} />
          </bs.Col>

          <bs.Col span={6}>
            <input.BBSelectField bean={shipmentDetail} label={'Stackable or Non Stackable?'} field={'stackable'}
              optionLabels={['Stackable', 'Non Stackable']}
              options={['STACKABLE', 'NON_STACKABLE']} onInputChange={this.onModifyShipmentDetail} />
          </bs.Col>

        </bs.Row>
      </div>
    )
  }

  renderSeaLCLOrAirSection = () => {
    const { observer } = this.props;
    let requestModel = observer.getMutableBean()
    let mode: TransportationMode = requestModel['mode'];
    if (!_settings.TransportationTool.isSeaLCL(mode) && !_settings.TransportationTool.isAir(mode)) return <></>;
    let shipmentDetail = observer.getBeanProperty('shipmentDetail', {});

    return (
      <>
        {this.renderDimensions()}
        <bs.Row>
          <bs.Col span={6}>
            <input.BBStringField required
              bean={shipmentDetail} field={"packageQty"} label={T('No. of package')}
              onInputChange={this.onModifyShipmentDetail} />
          </bs.Col>
          <bs.Col span={3}>
            <input.BBNumberField bean={shipmentDetail} label={T("CBM")} field={'volumeCbm'}
              onInputChange={this.onModifyShipmentDetail} required inputObserver={observer}
              validators={[POSITIVE_NUMBER_VALIDATOR]} />
          </bs.Col>
          <bs.Col span={3}>
            <input.BBNumberField
              bean={shipmentDetail} label={T("G.W (KGS)")} field={'grossWeightKg'} required
              onInputChange={this.onModifyShipmentDetail} inputObserver={observer} validators={[POSITIVE_NUMBER_VALIDATOR]} />
          </bs.Col>
        </bs.Row>
        <bs.Row>
          <bs.Col span={12}>
            <input.BBStringField bean={shipmentDetail} label={T('Special request')} field={'specialRequestNote'}
              placeholder='Special request : keep cool, degree?' onInputChange={this.onModifyShipmentDetail} />
          </bs.Col>
        </bs.Row>
      </>
    )
  }

  onChangeContainer = (bean: any, field: string, oldVal: any, newVal: any) => {
    if (newVal === oldVal) {
      return;
    }
    if (!newVal || newVal === '') {
      bean['reportVolume'] = 0;
      bean['reportVolumeUnit'] = 'TEU';
      this.onModifyShipmentDetail(bean, field, oldVal, newVal);
      return;
    }

    bean['reportVolumeUnit'] = 'TEU';
    bean['reportVolume'] = ContainerTypeUnit.calculateTUE(newVal);
    this.onModifyShipmentDetail(bean, field, oldVal, newVal);
  }

  renderSeaFCLSection = () => {
    const { observer } = this.props;
    let requestModel = observer.getMutableBean()
    let mode: TransportationMode = requestModel['mode'];
    if (!_settings.TransportationTool.isSeaFCL(mode)) return <></>;
    let shipmentDetail = observer.getBeanProperty('shipmentDetail', {});
    let label: string = 'Container Types';
    if (shipmentDetail['reportVolume']) label += `(${shipmentDetail['reportVolume']} TEU)`

    return (
      <>
        <bs.Row>
          <bs.Col span={12} md={6}>
            <BBContainerType bean={shipmentDetail} label={label} field={'volumeInfo'} required onInputChange={this.onChangeContainer} />
          </bs.Col>
          <bs.Col span={6} md={3}>
            <input.BBNumberField bean={shipmentDetail} label={T("G.W (KGS)")} field={'grossWeightKg'}
              onInputChange={this.onModifyShipmentDetail} />
          </bs.Col>
          <bs.Col span={6} md={3}>
            <input.BBNumberField bean={shipmentDetail} label={T("CBM")} field={'volumeCbm'}
              onInputChange={this.onModifyShipmentDetail} />
          </bs.Col>
        </bs.Row>

        <bs.Row>
          <bs.Col span={12} md={6}>
            <input.BBStringField bean={shipmentDetail} label={T('Req for free time/ terminal at dest')}
              field={'freeTimeTerminalRequest'} onInputChange={this.onModifyShipmentDetail}
              placeholder='Request for free time/ terminal at dest' />
          </bs.Col>
          <bs.Col span={12} md={6}>
            <input.BBStringField bean={shipmentDetail} label={T('Special container')}
              placeholder='Special container: Provide Temperature (If reefer container), DIM, GW, Picture of goods,..'
              field={'specialRequestNote'} onInputChange={this.onModifyShipmentDetail} />
          </bs.Col>
        </bs.Row>
      </>
    )
  }

  renderTruckingSection = () => {
    const { observer } = this.props;
    let requestModel = observer.getMutableBean()
    let mode: TransportationMode = requestModel['mode'];
    if (!_settings.TransportationTool.isTruck(mode)) return <></>;
    let shipmentDetail = observer.getBeanProperty('shipmentDetail', {});

    return (
      <>

        {this.renderDimensions()}
        <bs.Row>
          <bs.Col span={12} md={6}>
            {
              _settings.TransportationTool.isTruckContainer(mode)
                ?
                <BBContainerType bean={shipmentDetail} label={'Container Type'} field={'volumeInfo'} required onInputChange={this.onChangeContainer} />
                :
                <input.BBStringField bean={shipmentDetail} label={T('Truck Type')} field={'volumeInfo'} required
                  placeholder='VD: 4.2m/7.6m/9.6m/13.5m/17.5m/...' onInputChange={this.onModifyShipmentDetail} />
            }

          </bs.Col>
          <bs.Col span={6} md={3}>
            <input.BBNumberField bean={shipmentDetail} label={T("G.W (KGS)")} field={'grossWeightKg'}
              onInputChange={this.onModifyShipmentDetail} />
          </bs.Col>
          <bs.Col span={6} md={3}>
            <input.BBNumberField bean={shipmentDetail} label={T("CBM")} field={'volumeCbm'}
              onInputChange={this.onModifyShipmentDetail} />
          </bs.Col>
        </bs.Row>

        <bs.Row>
          <bs.Col span={12}>
            <input.BBStringField bean={shipmentDetail} label={T('VN Border Gate')} field={'vnBorderGateRequest'}
              placeholder='Cửa khẩu yêu cầu ở VN?' onInputChange={this.onModifyShipmentDetail} />
          </bs.Col>
        </bs.Row>
      </>
    )
  }

  popupUIUploadPreview(props: app.AppComponentProps, url: string) {
    let { pageContext } = props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (<module.communication.message.UIUploadPreview appContext={appCtx} pageContext={pageCtx} url={url} />);
    }
    pageContext.createPopupPage("preview", T("Preview"), createAppPage, { size: "lg" });
  }

  onRenderAttachFiles = () => {
    let { appContext, observer } = this.props;
    let requestModel: any = observer.getMutableBean();

    let attachFiles: Array<any> = [];
    let attachments: Array<any> = requestModel['attachments'] || []

    for (let i = 0; i < attachments.length; i++) {
      let att = attachments[i];
      attachFiles.push(
        <div key={`${i}-att-file`} className='flex-grow-0 flex-hbox m-1 border'>
          <div className='flex-vbox align-items-center' style={{ width: 120 }}>
            {module.communication.message.FACTORY.create(appContext, att['name'], att['resourceUri'])}
            <bs.Button className='p-0' laf='link' onClick={() => {
              this.popupUIUploadPreview(this.props, att['resourceUri']);
            }}>
              <div title={att['name']}>
                {util.text.formater.truncate(att['name'], 18, true)}
              </div>
            </bs.Button>
          </div>
          <bs.Button laf='link' className='p-0 flex-hbox justify-content-start' style={{ marginLeft: -15 }} onClick={() => {
            attachments.splice(i, 1);
            this.forceUpdate();
          }} >
            <FeatherIcon.X size={15} />
          </bs.Button>
        </div>
      )
    }
    return attachFiles;
  }

  pushAttachmentFile(attachmentFile: any) {
    let { observer } = this.props;
    let attachments: Array<any> = observer.getComplexArrayProperty('attachments', []);
    let size = attachmentFile['size'];
    if (this.attachmentsSize + size > 35 * 1000000) {
      bs.dialogShow("Notifications", <div>{`File size has exceeded 35M(Total Size ${this.attachmentsSize + size})`}</div>);
      return false;
    }
    this.attachmentsSize += size;
    attachments.push(attachmentFile);
    return true;
  }

  onUpload = (uploadResources: Array<any>) => {
    const { appContext, observer } = this.props;
    for (let sel of uploadResources) {
      sel['resourceUri'] = appContext.getServerContext().createRestURL("/upload/resource/" + sel['storeId']);
      sel['restURL'] = appContext.getServerContext().createRestURL("");
      if (!this.pushAttachmentFile(sel)) {
        break;
      };
    }
    this.onModify(observer.getMutableBean(), 'attachments', observer.getComplexArrayProperty('attachments', []), uploadResources)
  }

  onInputChange(event: any) {
    const { appContext } = this.props;
    let rest = appContext.getServerContext().getRestClient();
    let data = new FormData()
    for (let i = 0; i < event.target.files.length; i++) {
      let file = event.target.files[i];
      data.append('files', file);
    }
    let successCB = (result: any) => {
      let uploadResources: Array<component.UploadResource> = result.data;
      this.onUpload(uploadResources)
    }
    rest.formSubmit('upload/multi-file', data, successCB);
  }

  getMailMessage = () => {
    return this.containerRef.current?.innerHTML || '';
  }

  render() {
    const { appContext, pageContext, observer } = this.props;
    let requestModel = observer.getMutableBean();
    let shipmentDetail: any = observer.getBeanProperty('shipmentDetail', {});

    let mode: TransportationMode = requestModel['mode'];
    let locationTypes: any[] = ['Port'];
    if (_settings.TransportationTool.isAir(mode)) {
      locationTypes = ['Airport'];
    } else if (_settings.TransportationTool.isTruck(mode)) {
      locationTypes = ['State', 'Port', 'Airport'];
    }

    if (['EXW', 'DDP', 'DDU'].includes(requestModel['termOfService'])) {
      locationTypes.push('Country');
    }

    let mailCc: any[] = requestModel['mailCcBeans'] || []
    let mailTo: any[] = requestModel['mailToBeans'] || []

    let termOfServiceOptions: string[] = [
      'FOB', 'CFR', 'CIF', 'CPT', 'CIP',
      'DAP', 'DDU', 'DDP',
      'EXW', 'FCA', 'FAS',
    ];

    let termOfServiceLabels: string[] = [
      'FOB - Free On Board',
      'CFR - Cost and Freight',
      'CIF - Cost, Insurance and Freight',
      'CPT - Carriage Paid To',
      'CIP - Carriage and Insurance Paid To',
      'DAP - Delivered At Place',
      'DDU - Delivered at Place Unloaded',
      'DDP - Delivered Duty Paid',
      'EXW - Ex Works',
      'FCA - Free Carrier',
      'FAS - Free Alongside Ship',
    ];

    let labelLoading = _settings.TransportationTool.isAir(mode) ? 'Air Of Loading' : 'Port Of Loading';
    let labelDischarge = _settings.TransportationTool.isAir(mode) ? 'Air Of Discharge' : 'Port Of Discharge';

    return (
      <div className='flex-vbox'>
        <div className='flex-vbox rounded-bottom mx-md-2 px-md-2'>

          <bs.Row>
            <bs.Col span={12}>
              {/* <BBRefUserCustomer placeholder='Client...' label='Customer/ Lead' allowUserInput hideMoreInfo
                appContext={appContext} pageContext={pageContext} minWidth={400}
                bean={requestModel} beanIdField={'clientPartnerId'} beanLabelField={'clientLabel'}
                onPostUpdate={(input: any, bean: any, selectOpt: any) => this.onModify(requestModel, 'clientLabel', null, selectOpt['name'])} /> */}
              <BBRefBFSOnePartner placeholder='Customer/ Lead...' label='Customer/ Lead' allowUserInput
                style={{ width: '100%' }} placement='auto-start'
                appContext={appContext} pageContext={pageContext} minWidth={400} hideMoreInfo
                bean={requestModel} beanIdField={'clientPartnerId'} beanLabelField={'clientLabel'} partnerType='Customer'
                onPostUpdate={(input: any, bean: any, selectOpt: any) => this.onModify(requestModel, 'clientLabel', null, selectOpt['name'])} />
            </bs.Col>
          </bs.Row>

          <bs.Row>
            <bs.Col md={6}>
              <input.BBSelectField bean={requestModel} label={'Term of Service'} field={'termOfService'}
                options={termOfServiceOptions} optionLabels={termOfServiceLabels} onInputChange={this.onModify} />
            </bs.Col>
            <bs.Col md={6}>
              <input.BBDateTimeField
                label={T('Cargo Ready Date')} bean={requestModel}
                field={'cargoReadyDate'} dateFormat={'DD/MM/YYYY'} timeFormat={false} onInputChange={this.onModify} />
            </bs.Col>
          </bs.Row>

          {!_settings.TransportationTool.isTruck(mode) && (
            <bs.Row>
              <bs.Col span={12} md={6}>
                <BBRefLocation appContext={appContext} pageContext={pageContext} bean={requestModel} key={util.IDTracker.next()}
                  beanIdField={'fromLocationCode'} beanLabelField={'fromLocationLabel'} locationTypes={locationTypes}
                  hideMoreInfo
                  label={labelLoading} placeholder={labelLoading} refLocationBy='code' style={{ minWidth: 350 }}
                  onPostUpdate={(_inputUi, bean, selectOpt, _userInput) => {
                    bean['fromLocationCode'] = selectOpt['code'];
                    bean['fromLocationLabel'] = selectOpt['label'];
                    this.onModify(bean, 'fromLocationCode', null, selectOpt['code'])
                  }} />
              </bs.Col>

              <bs.Col span={12} md={6}>
                <BBRefLocation appContext={appContext} pageContext={pageContext} bean={requestModel} key={util.IDTracker.next()}
                  beanIdField={'toLocationCode'} beanLabelField={'toLocationLabel'} locationTypes={locationTypes}
                  hideMoreInfo
                  label={labelDischarge} placeholder={labelDischarge} refLocationBy='code' style={{ minWidth: 350 }}
                  onPostUpdate={(_inputUi, bean, selectOpt, _userInput) => {
                    bean['toLocationCode'] = selectOpt['code'];
                    bean['toLocationLabel'] = selectOpt['label'];
                    this.onModify(bean, 'toLocationCode', null, selectOpt['code'])
                  }} />
              </bs.Col>
            </bs.Row>
          )}

          <bs.Row>
            <bs.Col span={12} md={6}>
              <input.BBTextField style={{ height: '2rem' }}
                bean={requestModel} label={T('Pickup Address')} field={'pickupAddress'} onInputChange={this.onModify} />
            </bs.Col>
            <bs.Col span={12} md={6}>
              <input.BBTextField style={{ height: '2rem' }}
                bean={requestModel} label={T('Delivery Address')} field={'deliveryAddress'} onInputChange={this.onModify} />
            </bs.Col>
          </bs.Row>

          <bs.Row className='pt-md-1'>
            <bs.Col span={12} md={6}>
              <_settings.BBRefCommodityType key={shipmentDetail['commodity']} appContext={appContext} pageContext={pageContext} hideMoreInfo
                bean={shipmentDetail} placeholder='Commodity' label='Commodities / Descriptions Of Goods' beanIdField='commodity'
                onPostUpdate={(_inputUI, _bean, selectOpt, _userInput) => { this.onModifyShipmentDetail(shipmentDetail, 'commodity', null, selectOpt.id) }} />

              <input.BBTextField bean={shipmentDetail} field={"descOfGoods"} placeholder='Descriptions Of Goods, ...'
                onInputChange={this.onModifyShipmentDetail} />
            </bs.Col>

            <bs.Col span={12} md={6}>
              {
                _settings.TransportationTool.isAir(mode) &&
                <input.BBCheckboxField className='text-warning mt-1 border-top border-dashed' label='Express Courier?'
                  bean={shipmentDetail} field='expressCourier' value={false} onInputChange={this.onModifyShipmentDetail} />
              }

              {
                _settings.TransportationTool.isTruck(mode) &&
                <input.BBCheckboxField className='text-warning' label='Cross Border Trucking?' bean={shipmentDetail}
                  field='crossBorderTrucking' value={false} onInputChange={this.onModifyShipmentDetail} />
              }

              <input.BBCheckboxField className='text-warning' label='DG/ Liquid cargo?' bean={shipmentDetail}
                field='dgLiquidCargo' value={false} onInputChange={this.onModifyShipmentDetail} />

              <input.BBCheckboxField className='text-warning mt-1 border-top border-dashed' label='Request to buy insurance?'
                bean={shipmentDetail} field='buyInsuranceRequest' value={false} onInputChange={this.onModifyShipmentDetail} />
            </bs.Col>
          </bs.Row>

          {this.renderSeaFCLSection()}
          {this.renderSeaLCLOrAirSection()}
          {this.renderTruckingSection()}

          <bs.Row className='my-md-2'>

            <bs.Col span={12} md={6}>
              <input.BBTextField className='flex-vbox' label='Note' bean={requestModel} field={'note'} style={{ height: '3rem' }}
                placeholder='Other information, if any' onInputChange={this.onModify} />
            </bs.Col>

            <bs.Col span={12} md={6}>
              <input.BBTextField style={{ height: '3rem' }}
                bean={requestModel} label={T('Target rate (if any)')} field={'targetRate'} placeholder='Rate idea (If any)'
                onInputChange={this.onModify} />
            </bs.Col>
          </bs.Row>

          <div className='d-flex flex-column flex-md-row' key={util.IDTracker.next()}
            style={bs.ScreenUtil.isMobileScreen() ? {} : { minHeight: 300 }}>

            <div className='flex-vbox' style={{ maxWidth: 500, minWidth: 350 }}>

              <bs.CssTooltip position='top-right' width={400}>
                <bs.CssTooltipToggle>
                  <div className='d-flex flex-wrap py-2 px-1 my-1 bg-light border-bottom w-100'>
                    <span className='fw-bold me-1 text-primary' style={{ fontSize: '0.875rem' }}>Saleman: </span>
                    <span className='text-secondary' style={{ fontSize: '0.875rem' }}>
                      {`${requestModel.salemanLabel} (${requestModel.salemanEmail})`}
                    </span>
                  </div>
                </bs.CssTooltipToggle>
                <bs.CssTooltipContent className="d-flex flex-column rounded" >
                  <div className="tooltip-header mb-2">
                    <span className="tooltip-title">Danh sách email theo POL:</span>
                  </div>
                  <div className="ps-2">
                    <p className="fw-bold mb-1">Hải Phòng:</p>
                    <ul className="mb-2 ps-3">
                      <li>FCL: <EMAIL></li>
                      <li>LCL: <EMAIL></li>
                    </ul>

                    <p className="fw-bold mb-1">Hồ Chí Minh:</p>
                    <ul className="mb-2 ps-3">
                      <li>FCL: <EMAIL></li>
                      <li>LCL: <EMAIL></li>
                    </ul>

                    <p className="fw-bold mb-1">Đà Nẵng:</p>
                    <ul className="mb-0 ps-3">
                      <li>FCL: <EMAIL></li>
                    </ul>
                  </div>
                </bs.CssTooltipContent>
              </bs.CssTooltip>

              <div className='flex-vbox h-100' style={{ overflowY: 'auto', overflowX: 'hidden' }}>

                <div className='flex-vbox'>
                  <label className="form-label">{'Mail To:'}</label>
                  <module.communication.message.BBRefMultiEmail beanIdField='email' beanLabelField='label'
                    appContext={appContext} pageContext={pageContext} placeholder="Enter to email..." className='w-100'
                    placement="bottom-start" offset={[0, 5]} bean={mailTo} minWidth={400}
                    onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => this.onModify(requestModel, 'mailToBeans', null, _selectOpt)} />
                </div>

                <input.BBStringArrayField bean={requestModel} label='Mail To: (External)'
                  field={'externalEmailTo'} onInputChange={this.onModify} />

                <div className='flex-vbox border-top'>
                  <label className="form-label">{'Mail Cc:'}</label>
                  <module.communication.message.BBRefMultiEmail beanIdField='email' beanLabelField='label' className='w-100'
                    appContext={appContext} pageContext={pageContext} placeholder="Enter cc email..."
                    placement="bottom-start" offset={[0, 5]} bean={mailCc} minWidth={400}
                    onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) => this.onModify(requestModel, 'mailCcBeans', null, _selectOpt)} />
                </div>
              </div>
            </div>

            <div className='flex-vbox border border-start border-dashed mx-md-2'>
              <div className='flex-vbox flex-grow-0 justify-content-center align-items-start border-bottom border-dashed'>
                <p className='py-1 mx-2'>Attachments (PL, MSDS, xlsx, pdf, jpg, png, ...)</p>
              </div>

              <div className='flex-vbox'>
                <div className='flex-vbox flex-grow-0 border rounded-md border-dashed border-info p-2 mx-1'>
                  <input style={{ height: 80 }} type='file' name={`uploadFiles[]`} multiple={true}
                    onChange={event => this.onInputChange(event)} />
                </div>
                <bs.GreedyScrollable className='flex-vbox my-1'>
                  {this.onRenderAttachFiles()}
                </bs.GreedyScrollable>
              </div>
            </div>
          </div>

          <div ref={this.containerRef} style={{ width: '100%', height: '100%', display: 'none' }} key={this.viewId}>
            {/* Renders the email template with all props passed through */}
            <RequestPricingMailTemplate {...this.props} />
          </div>

        </div>
      </div>
    )
  }

}

export class UIMailRequestPricing extends entity.AppDbComplexEntityEditor {
  requestObserver: entity.ComplexBeanObserver;
  requestPricingRef: RefObject<UIRequestPricing>;

  state = {
    isSending: false
  };

  constructor(props: entity.AppComplexEntityEditorProps) {
    super(props);
    this.requestPricingRef = createRef<UIRequestPricing>();

    const { observer } = this.props;
    let requestModel: any = observer.getMutableBean();
    this.requestObserver = new entity.ComplexBeanObserver(requestModel);
  }

  componentDidMount(): void {
    const { appContext } = this.props;
    let requestModel: any = this.requestObserver.getMutableBean();
    const resend = requestModel['resend'] || false;
    if (!resend) {
      appContext.createHttpBackendCall('TransportPriceMiscService', 'initInquiryRequest',
        { request: requestModel })
        .withSuccessData((data: any) => {
          let request: any = onMapEmail(data);
          this.requestObserver = new entity.ComplexBeanObserver(request);
          this.nextViewId();
          this.forceUpdate()
        })
        .withFail(() => {
          let request: any = onMapEmail(requestModel);
          this.requestObserver = new entity.ComplexBeanObserver(request);
          this.nextViewId();
          this.forceUpdate()
        })
        .call();
    }
  }

  onRequestPricing = () => {
    const { appContext, observer } = this.props;
    let requestModel = this.requestObserver.getMutableBean();
    let mode: TransportationMode = requestModel['mode'];
    let purpose: ImportExportPurpose = requestModel['purpose']

    const isFromLocationMissing = !requestModel['fromLocationCode'];
    const isToLocationMissing = !requestModel['toLocationCode'];


    if (requestModel['termOfService'] === 'EXW' && purpose === 'IMPORT' && !requestModel['pickupAddress']) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Vui lòng nhập địa chỉ lấy hàng. (Pickup Address)')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      return;
    }

    if (!_settings.TransportationTool.isTruck(mode) && (isFromLocationMissing || isToLocationMissing)) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide both Port Of Loading and Port Of Discharge. (If EXW, DDP, DDU: You can enter Country instead of Port)')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      return;
    }

    let shipmentDetail: any = requestModel['shipmentDetail'] || {};
    let gw: number = shipmentDetail['grossWeightKg'] || 0;
    let cbm: number = shipmentDetail['volumeCbm'] || 0;
    let containerTypes: string = shipmentDetail['volumeInfo'] || '';
    let packageQty: number = shipmentDetail['packageQty'] || 0;

    if (_settings.TransportationTool.isSeaFCL(mode) && !containerTypes) {
      bs.dialogShow('Error',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide Container Types.')}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      return;
    } else if (_settings.TransportationTool.isSeaLCL(mode)) {
      if (cbm === 0) {
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide GrossWeight/ CBM.')}
          </div>,
          { backdrop: 'static', size: 'sm' }
        );
        return;
      }
      if (requestModel['termOfService'] === 'EXW' && packageQty === 0) {
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide Package Quantity.')}
          </div>,
          { backdrop: 'static', size: 'sm' }
        );
        return;
      }
    } else if (_settings.TransportationTool.isAir(mode)) {
      if (gw === 0) {
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide GrossWeight.')}
          </div>,
          { backdrop: 'static', size: 'sm' }
        );
        return;
      }
      if (requestModel['termOfService'] === 'EXW' && packageQty === 0) {
        bs.dialogShow('Error',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />{T('Please provide Package Quantity.')}
          </div>,
          { backdrop: 'static', size: 'sm' }
        );
        return;
      }
    }

    this.setState({ isSending: true });

    // Lấy mailMessage từ component con thông qua ref
    const mailMessage = this.requestPricingRef.current?.getMailMessage() || '';

    let mailTo: any[] = requestModel['mailToBeans'] || []
    let toEmails: string[] = mailTo.filter(sel => sel['email']).map(sel => sel['email'])

    let mailExtTo: any[] = requestModel['externalEmailTo'] || []

    let mailCc: any[] = requestModel['mailCcBeans'] || []
    let ccEmails: string[] = mailCc.filter(sel => sel['email']).map(sel => sel['email'])

    let attachments: Array<any> = this.requestObserver.getComplexBeanProperty('attachments', []);
    attachments.forEach(sel => sel['resourceUrl'] = encodeURI(sel['resourceUri']));

    const allTo: string[] = [...(toEmails || []), ...(mailExtTo || [])];

    if (allTo.length === 0) {
      let messageError: string = 'Please enter at least one email address to send the request.'
      bs.dialogShow('Error',
        <div className="text-warning fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />{messageError}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      return;
    }

    let mailRequest: any = {
      ...requestModel,
      from: requestModel['from'] || '',
      to: allTo,
      cc: [...(ccEmails || [])],
      attachments: attachments || [],
      mailMessage: mailMessage || '',
    }

    if (observer.getBeanProperty('purpose')) mailRequest['purpose'] = observer.getBeanProperty('purpose');

    appContext.createHttpBackendCall('TransportPriceMiscService', 'sendInquiryRequest',
      { request: mailRequest })
      .withSuccessData((newRequest: any) => {
        this.requestObserver = new entity.ComplexBeanObserver(onMapEmail(newRequest));
        this.setState({ isSending: false });
        this.onPostCommit(this.requestObserver.getMutableBean());
      })
      .withEntityOpNotification('commit', 'Send Request Success!!!!')
      .withFail(() => {
        this.setState({ isSending: false });
      })
      .call();
  }

  render(): React.ReactNode {
    if (this.isLoading()) return this.renderLoading();
    let sended: boolean = !this.requestObserver.isNewBean();

    return (
      <div className="flex-vbox notranslate" translate="no" key={this.viewId}>
        <UIRequestPricing {...this.props} observer={this.requestObserver} ref={this.requestPricingRef} />

        <div className="flex-hbox flex-grow-0 mt-2 w-100">

          <bs.Button laf={sended ? 'success' : 'info'} className="border-0 py-2 px-2 mx-md-2 w-100 h-100"
            onClick={this.onRequestPricing} disabled={this.state.isSending || sended}>
            <FeatherIcon.Mail className='mx-1' size={12} /> {this.state.isSending ? 'Sending...' : !sended ? 'Send Request' : 'Send Request (Success)'}
          </bs.Button>

        </div>
      </div>
    )
  }
}

export function onShowUIRequestPricing(uiList: app.AppComponent, request: any = {}, onPostAction?: (_entity: any) => void) {
  const { pageContext } = uiList.props

  const onPostCommit = (_entity: any, uiEditor?: app.AppComponent) => {
    uiEditor?.props.pageContext.back();
    if (onPostAction) onPostAction(_entity);
  }

  const createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => (
    <div className='flex-hbox'>
      <UIMailRequestPricing appContext={appCtx} pageContext={pageCtx}
        observer={new entity.ComplexBeanObserver(request)} onPostCommit={onPostCommit} />
    </div>
  );
  let type: string = _settings.mapToTypeOfShipment(request['purpose'], request['mode']) || 'N/A';

  // We avoid using bs.ScreenUtil.getScreenWidth (window.screen.width) because it may not account for certain UI elements like scrollbars
  //  Or when the user resizes the browser window
  let size: 'flex-lg' | 'xl' = 'flex-lg';
  if (window.innerWidth < 1300) {
    size = 'xl';
  }
  pageContext.createPopupPage('mail-request-pricing', `Request Pricing(${type})`, createPageContent, { size: size, backdrop: 'static' });
}

