package cloud.datatp.fforwarder.price.groovy

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject
import org.springframework.context.ApplicationContext

class InquiryRequestSql extends Executor {

    public class SearchInquiryRequest extends ExecutableSqlBuilder {

        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

            String query = """
                WITH filtered_requests AS (
                    SELECT c.*
                    FROM lgc_price_inquiry_request c
                    WHERE ${FILTER_BY_STORAGE_STATE("c", sqlParams)}
                        ${AND_FILTER_BY_PARAM("c.purpose", "groupType", sqlParams)}
                        ${AND_FILTER_BY_PARAM("c.mode", "mode", sqlParams)}
                        ${AND_FILTER_BY_OPTION("c.status", "status", sqlParams)}
                        ${AND_FILTER_BY_PARAM('c.saleman_account_id', 'salemanAccountId', sqlParams)}
                        ${AND_FILTER_BY_PARAM('c.pricing_account_id', 'pricingAccountId', sqlParams)}
                        ${AND_SEARCH_BY_PARAMS(['c.code', "c.mail_subject", "c.saleman_email", "c.mail_to", "c.mail_cc"], "search", sqlParams)}
                        ${addAndClause(sqlParams, "mailPattern", "(c.mail_to ILIKE '%' || :mailPattern || '%' OR c.pricing_account_id = :accessAccountId)")}
                        ${AND_FILTER_BY_RANGE("c.request_date", "requestDate", sqlParams)}
                        ${AND_FILTER_BY_RANGE("c.pricing_date", "pricingDate", sqlParams)}
                        AND (
                            'System' = :space
                            OR ('Company' = :space AND c.company_id = :companyId)
                            OR ('User' = :space AND (c.pricing_account_id = :accessAccountId OR c.saleman_account_id = :accessAccountId))
                        )
                )
                SELECT
                    r.*,
                    r.mode as mode
                FROM filtered_requests r
                ORDER BY r.request_date DESC
                ${MAX_RETURN(sqlParams)}
            """
            return query;
        }
    }

    public class SearchInquiryRequestSpace extends ExecutableSqlBuilder {

        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

            String query = """
                WITH filtered_requests AS (
                    SELECT c.*
                    FROM lgc_price_inquiry_request c
                    WHERE ${FILTER_BY_STORAGE_STATE("c", sqlParams)}
                      ${AND_FILTER_BY_OPTION("c.status", "status", sqlParams)}
                      ${AND_FILTER_BY_RANGE("c.request_date", "requestDate", sqlParams)}
                      ${AND_FILTER_BY_RANGE("c.pricing_date", "pricingDate", sqlParams)}
                      ${AND_FILTER_BY_PARAM("c.id", "requestIds", sqlParams)}
                      AND (
                        'System' = :space
                        OR ('Company' = :space AND c.company_id = :companyId)
                        OR ('User' = :space AND c.company_id = :companyId AND
                              (c.pricing_account_id = :accessAccountId OR c.saleman_account_id = :accessAccountId)
                           )
                      )
                )
                SELECT
                    r.*,
                    r.mode as mode
                FROM filtered_requests r
                ORDER BY r.request_date DESC
                ${MAX_RETURN(sqlParams)}
            """
            return query;
        }
    }

    public class InquiryRequestReport extends ExecutableSqlBuilder {
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

            String query = """
              SELECT
                    c.id,
                    c.code,
                    c.request_date,
                    c.pricing_date,
                    c.saleman_account_id,
                    c.saleman_label,
                    c.pricing_account_id,
                    c.pricing_label,
                    c.report_volume,
                    c.report_volume_unit,
                    c.from_location_code,
                    c.from_location_label,
                    c.to_location_code,
                    c.to_location_label,
                    c.mode as mode,
                    c.purpose as purpose,
                    c.total_step_counting,
                    c.status,
                    c.company_id
                FROM lgc_price_inquiry_request c
                WHERE ${FILTER_BY_STORAGE_STATE("c", sqlParams)}
                    ${AND_FILTER_BY_OPTION("c.status", "status", sqlParams)}
                    ${AND_FILTER_BY_PARAM('c.mode', 'mode', sqlParams)}
                    ${AND_FILTER_BY_PARAM('c.purpose', 'purpose', sqlParams)}
                    ${AND_FILTER_BY_PARAM('c.saleman_account_id', 'salemanAccountIds', sqlParams)}
                    ${AND_FILTER_BY_RANGE("c.request_date", "requestDate", sqlParams)}
                    ${AND_FILTER_BY_RANGE("c.pricing_date", "pricingDate", sqlParams)}
                    AND (
                      'System' = :space OR
                      ('Company' = :space AND c.company_id = :companyId) OR
                      ('User' = :space AND c.pricing_account_id = :accessAccountId)
                    )
            """
            return query;
        }
    }

    public class InquiryRequestCompanyAndDepartmentInfo extends ExecutableSqlBuilder {
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

            String query = """
                SELECT
                    cc.id AS company_id,
                    cc.label AS company_label,
                    COALESCE(dept_info.account_id, NULL) AS account_id,
                    COALESCE(dept_info.dept_id, NULL) AS dept_id,
                    COALESCE(dept_info.department_name, '') AS department_name,
                    COALESCE(dept_info.department_label, '') AS department_label,
                    COALESCE(dept_info.parent_department_label, '') AS parent_department_label
                FROM company_company cc
                LEFT JOIN (
                    SELECT DISTINCT ON (empl.account_id, dept.company_id)
                        dept.company_id AS dept_company_id,
                        empl.account_id AS account_id,
                        dept.id AS dept_id,
                        dept.name AS department_name,
                        dept.label AS department_label,
                        parent_dept.label AS parent_department_label
                    FROM company_hr_department dept
                    JOIN company_hr_department_employee_rel dept_ref
                        ON dept_ref.department_id = dept.id
                    JOIN company_hr_employee empl
                        ON empl.id = dept_ref.employee_id
                    LEFT JOIN company_hr_department parent_dept
                        ON parent_dept.id = SPLIT_PART(dept.parent_id_path, '/', 1)::INTEGER
                    WHERE ${FILTER_BY_PARAM('empl.account_id', 'accountIds', sqlParams)}
                    ORDER BY empl.account_id, dept.company_id,
                      CASE 
                          WHEN parent_dept.label = 'SALES' THEN 1
                          ELSE 2
                      END ASC
                ) dept_info ON dept_info.dept_company_id = cc.id
                WHERE ${FILTER_BY_PARAM('cc.id', 'companyIds', sqlParams)}
            """
            return query;
        }
    }

    public InquiryRequestSql() {
        register(new SearchInquiryRequest());
        register(new SearchInquiryRequestSpace());
        register(new InquiryRequestReport());
        register(new InquiryRequestCompanyAndDepartmentInfo());
    }
}