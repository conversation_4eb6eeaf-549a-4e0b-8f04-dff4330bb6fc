package cloud.datatp.fforwarder.price;

import cloud.datatp.fforwarder.price.calculator.AdditionalChargeCalculator;
import cloud.datatp.fforwarder.price.entity.AirTransportCharge;
import cloud.datatp.fforwarder.price.repository.AirTransportChargeRepository;
import groovy.lang.Binding;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlQueryManager.QueryContext;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.module.hr.entity.Employee;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.StringUtil;
import net.datatp.util.text.TokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class AirPriceLogic extends TransportPriceLogic {

  @Autowired
  private AirTransportChargeRepository repo;

  public List<SqlMapRecord> search(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/price/groovy/AirTransportChargeSql.groovy";
    sqlParams.addParam("companyId", company.getId());
    List<Long> companyIdPaths = company.findCompanyIdPaths();
    sqlParams.addParam("companyIds", companyIdPaths);
    
    List<Company> descendantsCompanies = companyReadLogic.findByParentIds(client, companyIdPaths);
    Set<Long> descendantsCompanyIds = descendantsCompanies.stream().map(Company::getId).collect(Collectors.toSet());
    descendantsCompanyIds.addAll(companyIdPaths);
    sqlParams.addParam("descendantsCompanyIds", descendantsCompanyIds);

    return searchDbRecords(client, scriptDir, scriptFile, "SearchAirTransportCharge", sqlParams);
  }

  public AirTransportCharge getById(ClientInfo client, Company company, Long id) {
    return repo.findById(id).get();
  }

  public List<AirTransportCharge> findByIds(ClientInfo client, Company company, List<Long> ids) {
    return repo.findByIds(ids);
  }

  public List<AirTransportCharge> findAirTransportChargeExpired(ClientInfo client, Company company, Date currentDate) {
    return repo.findAirTransportChargesExpired(company.getId(), currentDate);
  }

  public AirTransportCharge saveAirPrice(ClientInfo client, Company company, AirTransportCharge charge) {
    if (charge.isNew()) {
      final String code = generateTransportPriceCode(charge);
      charge.setCode(code);
      final Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
      Objects.assertNotNull(employee, "Employee not found!!!, login id: " + client.getRemoteUser());
      charge.setAssigneeAccountId(employee.getAccountId());
      charge.setAssigneeLabel(employee.getLabel());
    } else {
      Objects.assertTrue(charge.isSameCompany(company.getId()), "Not allowed to save data with the different company");
    }
    final String fromLocCode = charge.getFromLocationCode();
    final String label = TokenUtil.labelWithToken(fromLocCode, charge.getToLocationCode());
    charge.setLabel(label.toUpperCase());
    charge.setRoute(charge.getFromLocationCode() + "-" + charge.getToLocationCode());
    if (StringUtil.isEmpty(charge.getCarrierLabel())) charge.setCarrierRoute(charge.getRoute() + "-" + "N/A");
    else charge.setCarrierRoute(charge.getRoute() + "-" + charge.getCarrierLabel());
    AdditionalChargeCalculator.calculate(charge.getAdditionalCharges());
    charge.set(client, company);
    return repo.save(charge);
  }

  public List<MapObject> saveAirTransportCharges(ClientInfo client, Company company, List<MapObject> modified) {
    if (Collections.isNotEmpty(modified)) {
      for (MapObject sel : modified) {
        final Long id = sel.getLong("id", null);
        AirTransportCharge price = new AirTransportCharge();
        if (id != null) {
          price = getById(client, company, id);
          Objects.assertNotNull(price, "Air Charge not found: id = " + id);
        }
        price = price.computeFromMapObject(sel);
        AirTransportCharge updated = saveAirPrice(client, company, price);
        sel.put("id", updated.getId());
      }
    }
    return modified;
  }

  public boolean updateStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    List<Long> chargeIds = req.getEntityIds();
    if (chargeIds.isEmpty()) throw RuntimeError.IllegalArgument("No Records were selected!!");
    repo.setStorageState(chargeIds, req.getNewStorageState());
    return true;
  }

  public List<AirTransportCharge> findByCompany(ClientInfo client, Company company) {
    return repo.findByCompanyId(company.getId());
  }

  public boolean deleteByIds(ClientInfo client, Company company, List<Long> ids) {
    List<AirTransportCharge> charges = findByIds(client, company, ids);
    for (AirTransportCharge charge : charges) {
      Objects.assertTrue(charge.isSameCompany(company.getId()), "Not allowed to delete data with the different company");
    }
    DBConnectionUtil connectionUtil = new DBConnectionUtil(getJdbcDataSource());
    DeleteGraphBuilder deleteGraphBuilder = new DeleteGraphBuilder(connectionUtil, company.getId(), AirTransportCharge.class, ids);
    int count = deleteGraphBuilder.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return count > 0;
  }

  public List<SqlMapRecord> findHistoricalRoutes(ClientInfo client, Company company, MapObject params) {
    final String scriptName = "FindAirHistoricalRoutes.groovy";
    QueryContext queryContext = sqlQueryManager.create(QUERY_SCRIPT_DIR, scriptName);
    if (!params.containsKey("carrier_label")) params.put("carrier_label", null);
    if (!params.containsKey("handling_agent_partner_label")) params.put("handling_agent_partner_label", null);
    if (!params.containsKey("purpose")) params.put("purpose", null);
    if (!params.containsKey("pricing_creator_id")) params.put("pricing_creator_id", null);
    MapSqlParameterSource parameterSource = new MapSqlParameterSource(params);
    parameterSource.addValue("company_id", company.getId());
    final SqlSelectView view = queryContext.createSqlSelectView(new Binding(), parameterSource);
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }
}