package cloud.datatp.fforwarder.sales.groovy.bfsone

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import org.springframework.context.ApplicationContext

class BFSOneCustomerLeadsSql extends Executor {
    public class SelectBFSOneCustomerLead extends ExecutableSqlBuilder {

        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            String query = """
                SELECT 
                  o.ContactID                 AS contact_id,
                  o.DateCreated               AS date,
                  o.DateModified              AS modified_date,
                  o.CompanyName               AS name,
                  o.FristName                 AS personal_contact,
                  o.Onomatology               AS onomatology,
                  o.CellPhone                 AS phone,
                  o.Email                     AS email,
                  o.Street                    AS address,
                  o.UserName                  AS username,
                  o.Notes                     AS note
                FROM PartnerContact o
                WHERE (o.PartnerID is null or o.PartnerID = '')
                  AND o.UserName is not null
                  AND o.ContactID like 'LEA%' 
                  AND CAST(o.DateModified AS DATE) = CAST(GETDATE() AS DATE)
                ORDER BY o.UserName ASC
              """;
            return query;
        }
    }

    public BFSOneCustomerLeadsSql() {
        register(new SelectBFSOneCustomerLead());
    }
}