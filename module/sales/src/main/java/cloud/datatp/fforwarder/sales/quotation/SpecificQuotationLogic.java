package cloud.datatp.fforwarder.sales.quotation;

import cloud.datatp.fforwarder.price.AirPriceLogic;
import cloud.datatp.fforwarder.price.InquiryRequestLogic;
import cloud.datatp.fforwarder.price.SeaPriceLogic;
import cloud.datatp.fforwarder.price.entity.AirTransportCharge;
import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import cloud.datatp.fforwarder.price.entity.SeaFclTransportCharge;
import cloud.datatp.fforwarder.price.entity.SeaLclTransportCharge;
import cloud.datatp.fforwarder.sales.common.CustomerChargeLogic;
import cloud.datatp.fforwarder.sales.common.entity.QuotationAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.QuotationCharge;
import cloud.datatp.fforwarder.sales.inquiry.InquiryLogic;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import cloud.datatp.fforwarder.sales.quotation.dto.ConfirmQuotationModel;
import cloud.datatp.fforwarder.sales.quotation.dto.LocalCharge;
import cloud.datatp.fforwarder.sales.quotation.dto.Params;
import cloud.datatp.fforwarder.sales.quotation.entity.GenericQuotation;
import cloud.datatp.fforwarder.sales.quotation.entity.SpecificQuotation;
import cloud.datatp.fforwarder.sales.quotation.repository.SpecificQuotationRepository;
import cloud.datatp.fforwarder.settings.TransportationMode;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Getter;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.communication.MailMessage;
import net.datatp.module.company.entity.Company;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.core.security.entity.Capability;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.module.graphapi.GraphApiService;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.ErrorType;
import net.datatp.util.error.RuntimeError;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Getter
public class SpecificQuotationLogic extends DAOService {

  @Autowired
  private SpecificQuotationRepository sQuotationRepo;

  @Autowired
  private InquiryLogic inquiryLogic;

  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private SeqService seqService;

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private GraphApiService graphApiService;

  @Autowired
  private InquiryRequestLogic inquiryRequestLogic;

  @Autowired
  private CustomerChargeLogic customerChargeLogic;

  @Autowired
  private AirPriceLogic airPriceLogic;

  @Autowired
  private SeaPriceLogic seaPriceLogic;

  @PostConstruct
  private void onInit() {
    seqService.createIfNotExists(GenericQuotation.SEQUENCE, 10);
  }

  public List<SpecificQuotation> findByCompany(ClientInfo client, Company company) {
    return sQuotationRepo.findByCompany(company.getId());
  }

  private SpecificQuotation computeSQuotationModel(ClientInfo client, Company company, SpecificQuotation quotation) {
    List<QuotationCharge> quoteList = customerChargeLogic.findQuotationChargeBySQuotationId(company, quotation.getId());
    quotation.setQuoteList(quoteList);
    List<QuotationAdditionalCharge> addCharges = customerChargeLogic.findAddChargeBySQuotationId(company, quotation.getId());
    quotation.withAdditionalCharges(addCharges);
    return quotation;
  }

  public SpecificQuotation getById(ClientInfo client, Company company, Long id) {
    final SpecificQuotation quotation = sQuotationRepo.getById(company.getId(), id);
    Objects.assertNotNull(quotation, "Quotation not found, Reference ID = {} ", id);
    return computeSQuotationModel(client, company, quotation);
  }

  public SpecificQuotation saveSpecificQuotation(ClientInfo client, Company company, SpecificQuotation quotation) {
    final boolean isNewQuote = quotation.isNew();
    SpecificServiceInquiry inquiry = quotation.getInquiry();
    if (isNewQuote) inquiry = inquiryLogic.newSpecificServiceInquiry(client, company, inquiry);
    List<QuotationCharge> quoteList = quotation.getQuoteList();
    List<LocalCharge> localHandlingCharges = quotation.getLocalHandlingCharges();

    String referenceCode = inquiry.getReferenceCode();
    InquiryRequest inquiryRequest = inquiryRequestLogic.getInquiryRequest(client, company, referenceCode);
    if(inquiryRequest == null) inquiryRequest = inquiry.toInquiryRequest();
    inquiryRequest = inquiryRequestLogic.saveInquiryRequest(client, company, inquiryRequest);
    inquiry.setReferenceCode(inquiryRequest.getCode());

    /* save quotation */
    quotation.setInquiry(inquiry);
    quotation.set(client, company);
    quotation = sQuotationRepo.save(quotation);

    if (!isNewQuote) {
      customerChargeLogic.deleteAdditionalCharge(client, company, quotation.getId());
    }

    List<QuotationAdditionalCharge> charges = QuotationAdditionalCharge.computeFromLocalCharges(localHandlingCharges);
    customerChargeLogic.saveAdditionalCharges(client, company, quotation.getId(), charges);

    List<QuotationCharge> holder = new ArrayList<>();
    for (QuotationCharge quote : quoteList) {
      quote.computeFromInquiry(inquiry);
      quote.setSpecificQuotationId(quotation.getId());
      QuotationCharge updateQuote = customerChargeLogic.saveQuotationCharge(client, company, quote);
      holder.add(updateQuote);
    }
    quotation.setQuoteList(holder);
    return quotation;
  }

  public SpecificQuotation newSpecificQuotation(ClientInfo client, Company company, Params.SQuotationCreation template) {
    SpecificServiceInquiry inquiry = template.getInquiry();
    List<Long> priceReferenceIds = template.getPriceReferenceIds();
    Long inquiryRequestId = template.getInquiryRequestId();

    SpecificQuotation quotation;

    if (inquiry != null) {
      quotation = new SpecificQuotation(template.getInquiry());
    } else if (inquiryRequestId != null) {
      InquiryRequest request = inquiryRequestLogic.getInquiryRequest(client, company, inquiryRequestId);
      Objects.assertNotNull(request, "Inquiry request not found: " + inquiryRequestId);
      inquiry = new SpecificServiceInquiry();
      inquiry.computeFromRequest(request);
      quotation = new SpecificQuotation(template.getInquiry());
    } else {
      throw new RuntimeError(ErrorType.IllegalArgument, "Missing inquiry or request!!");
    }

    TransportationMode mode = inquiry.getMode();
    if (TransportationMode.isAirTransport(mode)) {
      List<AirTransportCharge> foundPrices = airPriceLogic.findByIds(client, company, priceReferenceIds);
      quotation.withAirQuotes(foundPrices);
    } else if (TransportationMode.isSeaFCLTransport(mode)) {
      List<SeaFclTransportCharge> foundPrices = seaPriceLogic.findSeaFclTransportChargeByIds(client, company, priceReferenceIds);
      quotation.withFCLQuotes(foundPrices);
    } else if (TransportationMode.isSeaLCLTransport(mode)) {
      List<SeaLclTransportCharge> foundSeaCharges = seaPriceLogic.findSeaLclTransportChargeByIds(client, company, priceReferenceIds);
      quotation.withLCLQuotes(foundSeaCharges);
    } else {

    }
    return quotation;
  }

  public SpecificQuotation copySpecificQuotation(ClientInfo client, Company company, Long quotationId) {
    SpecificQuotation source = getById(client, company, quotationId);
    Objects.assertNotNull(source, "Quotation {} is not found", quotationId);

    List<QuotationCharge> quoteListSrc = source.getQuoteList();

    SpecificQuotation clone = source.clone().clearIds();
    clone.setQuoteList(new ArrayList<>());
    clone.setEditMode(EditMode.DRAFT);

    SpecificServiceInquiry inquiry = clone.getInquiry();
    inquiry.setReferenceCode(null);
    inquiry.setRequestDate(new Date());
    inquiry.setCargoReadyDate(new Date());
    inquiry.setEstimatedTimeDeparture(new Date());
    inquiry.clearIds();
    clone.setInquiry(inquiry);

    // Use LinkedHashMap to maintain insertion order while ensuring unique carrierLabel (Để luôn lấy record đầu tiên cho mỗi carrier)
    Map<String, QuotationCharge> quotes = new LinkedHashMap<>();
    for (QuotationCharge quote : quoteListSrc) {
      String carrierLabel = quote.getCarrierLabel();
      if (!quotes.containsKey(carrierLabel)) {
        QuotationCharge seaQuote = DataSerializer.JSON.clone(quote);
        seaQuote.setId(null);
        seaQuote.setValidity(new Date());
        quotes.put(carrierLabel, seaQuote);
      }
    }
    List<QuotationCharge> quoteList = new ArrayList<>(quotes.values());
    clone.setQuoteList(quoteList);
    return clone;
  }

  public List<SqlMapRecord> searchSpecificQuotation(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (permission == null) return new ArrayList<>();
    Account account = accountLogic.getActiveAccountByLoginId(client, client.getRemoteUser());
    Objects.assertNotNull(account, "Account not found!!!, login id: " + client.getRemoteUser());
    sqlParams.addParam("companyId", company.getId());
    sqlParams.addParam("accessAccountId", account.getId());

    if (permission.isGroupScope()) {
      String coreScriptDir = appEnv.addonPath("core", "groovy");
      String coreScriptFile = "net/datatp/module/hr/groovy/EmployeeSql.groovy";
      List<SqlMapRecord> accountIds = searchDbRecords(client, coreScriptDir, coreScriptFile, "FindEmployeeIdsByManagerId", sqlParams);
      List<Long> participantAccountIds = accountIds.stream()
        .map(record -> record.getLong("accountId", null))
        .collect(Collectors.toList());
      sqlParams.addParam("salemanAccountIds", participantAccountIds);
    }

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SpecificQuotationSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SelectSpecificQuotation", sqlParams);
  }

  public boolean changeSpecificQuotationStorageState(ClientInfo client, ChangeStorageStateRequest req) {
    sQuotationRepo.setStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public int deleteByIds(ClientInfo client, Company company, List<Long> ids) {
    SqlQueryParams sqlParams = new SqlQueryParams();
    sqlParams.addParam("ids", ids);
    sqlParams.addParam("companyId", company.getId());
    final Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
    Objects.assertNotNull(employee, "Employee {} is not found", client.getRemoteUser());
    final AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    final boolean writeCap = permission.getCapability().hasCapability(Capability.Write);
    sqlParams.addParam("writeCap", writeCap);
    sqlParams.addParam("dataScope", permission.getDataScope().name());
    List<Long> participants = Arrays.asList(client.getAccountId());
    if (permission.isGroupScope())
      participants = employeeLogic.findEmployeesByManagerId(client, company, employee.getId());
    sqlParams.addParam("participants", participants);
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SpecificQuotationSql.groovy";
    List<SqlMapRecord> recordsCounting = searchDbRecords(client, scriptDir, scriptFile, "QuotationDeleteValidate", sqlParams);
    Objects.assertTrue(recordsCounting.size() == 1, "Only one record is expected!!!");
    final SqlMapRecord record = recordsCounting.get(0);
    final Long validCount = record.getLong("validCounting", -1L);
    Objects.assertTrue(validCount - ids.size() == 0, "Only {} records can be deleted", validCount);
    DBConnectionUtil connectionUtil = new DBConnectionUtil(getJdbcDataSource());
    DeleteGraphBuilder graph = new DeleteGraphBuilder(connectionUtil, company.getId(), SpecificQuotation.class, ids);
    System.out.println("---------------------------------");
    graph.dumpQuery();
    System.out.println("---------------------------------");
    final int deleteCount = graph.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return deleteCount;
  }

  public ConfirmQuotationModel initConfirmQuotationModel(
    ClientInfo client, Company company, ConfirmQuotationModel template) {
    template = Objects.ensureNotNull(template, ConfirmQuotationModel::new);
    return template;
  }

  public ConfirmQuotationModel sendQuotation(ClientInfo client, Company company, ConfirmQuotationModel template) {
    MailMessage mailMessage = new MailMessage();
    mailMessage.setMessage(template.getMailMessage());
    mailMessage.setFrom(template.getSalemanEmail());
    mailMessage.setSubject(template.getMailSubject());
    mailMessage.setTo(template.getToList());
    mailMessage.setCc(template.getCcList());
    mailMessage.setAttachments(template.getAttachments());
    graphApiService.sendEmailWithHtmlFormat(client, company, mailMessage);
    return template;
  }

}