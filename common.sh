#!/usr/bin/env bash

window=false
if [ "$OSTYPE" = "msys" ] ; then
  window=true;
elif [[ "$OSTYPE" == "cygwin" ]]; then
  window=true;
elif [[ "$OSTYPE" == "win32" ]]; then
  window=true;
elif [[ "$OSTYPE" == "darwin20.0" ]]; then
  window=true;
fi

bin=`dirname "$0"`
ROOT_DIR=`cd $bin/..; pwd`

if $window; then
  ROOT_DIR=`cygpath --absolute --windows "$ROOT_DIR"`
fi

function has_opt() {
  OPT_NAME=$1
  shift
  #Par the parameters
  for i in "$@"; do
    if [[ $i == $OPT_NAME ]] ; then
      echo "true"
      return
    fi
  done
  echo "false"
}

function get_opt() {
  OPT_NAME=$1
  DEFAULT_VALUE=$2
  shift

  #Par the parameters
  for i in "$@"; do
    index=$(($index+1))
    if [[ $i == $OPT_NAME* ]] ; then
      value="${i#*=}"
      echo "$value"
      return
    fi
  done
  echo $DEFAULT_VALUE
}

declare -a PROJECTS=("datatp-core" "datatp-erp" "datatp-logistics" "datatp-document-ie" "datatp-crm" "datatp-docs" "datatp-build")

function runInProjects() {
  CMD=$1
  shift

  for project in "${PROJECTS[@]}"
  do
    echo "Run $CMD $@ in  $project"
    echo "----------------------------------------------------------------"
    cd $ROOT_DIR/$project
    if ! $CMD "$@"; then
      echo command returned some error
      #exit 0
    fi
    echo "----------------------------------------------------------------"
  done
}

